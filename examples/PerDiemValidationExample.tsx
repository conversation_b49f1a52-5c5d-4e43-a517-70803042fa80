/**
 * Example implementation showing how to use per diem validation functions
 * This demonstrates the key differences between distance rate and per diem validation
 */

import React, {useMemo} from 'react';
import useOnyx from '@hooks/useOnyx';
import {getPerDiemCustomUnit} from '@libs/PolicyUtils';
import {
    createPerDiemAllEligibleTransactionsSelector,
    createPerDiemEligibleTransactionsSelector,
    hasPerDiemRateTransactions,
    hasPerDiemSubRateTransactions,
    validatePerDiemTransactionEligibility,
} from '@libs/PolicyPerDiemValidationUtils';
import ONYXKEYS from '@src/ONYXKEYS';
import type {Rate} from '@src/types/onyx/Policy';

interface PerDiemValidationExampleProps {
    policyID: string;
}

function PerDiemValidationExample({policyID}: PerDiemValidationExampleProps) {
    const [policy] = useOnyx(`${ONYXKEYS.COLLECTION.POLICY}${policyID}`);
    const customUnit = getPerDiemCustomUnit(policy);
    const customUnitRates: Record<string, Rate> = useMemo(() => customUnit?.rates ?? {}, [customUnit]);

    // Create a set of all rate IDs for filtering
    const rateIDs = new Set(Object.keys(customUnitRates));

    // Example 1: Get all eligible transactions for the entire per diem custom unit
    const allEligibleTransactionsSelector = useMemo(() => {
        if (!customUnit?.customUnitID || rateIDs.size === 0) {
            return () => undefined;
        }
        return createPerDiemAllEligibleTransactionsSelector(customUnit.customUnitID, rateIDs);
    }, [customUnit?.customUnitID, rateIDs]);

    const [eligibleTransactionsData] = useOnyx(ONYXKEYS.COLLECTION.TRANSACTION, {
        selector: allEligibleTransactionsSelector,
        canBeMissing: true,
    });

    // Example 2: Get eligible transactions for a specific rate and subrate
    const specificRateID = 'example-rate-id';
    const specificSubRateID = 'example-subrate-id';
    
    const specificEligibleTransactionsSelector = useMemo(() => {
        if (!customUnit?.customUnitID) {
            return () => new Set<string>();
        }
        return createPerDiemEligibleTransactionsSelector(
            customUnit.customUnitID,
            specificRateID,
            specificSubRateID
        );
    }, [customUnit?.customUnitID, specificRateID, specificSubRateID]);

    const [specificEligibleTransactionIDs] = useOnyx(ONYXKEYS.COLLECTION.TRANSACTION, {
        selector: specificEligibleTransactionsSelector,
        canBeMissing: true,
    });

    // Example 3: Get transaction violations for eligible transactions
    const [transactionViolations] = useOnyx(ONYXKEYS.COLLECTION.TRANSACTION_VIOLATIONS, {
        selector: (violations) => {
            const eligibleTransactionIDs = eligibleTransactionsData?.transactionIDs;
            if (!eligibleTransactionIDs || eligibleTransactionIDs.size === 0) {
                return undefined;
            }
            return Object.fromEntries(
                Object.entries(violations ?? {}).filter(([key]) => {
                    const id = key.replace(ONYXKEYS.COLLECTION.TRANSACTION_VIOLATIONS, '');
                    return eligibleTransactionIDs?.has(id);
                }),
            );
        },
        canBeMissing: true,
    });

    // Example 4: Check if rates/subrates can be deleted
    const [allTransactions] = useOnyx(ONYXKEYS.COLLECTION.TRANSACTION);
    
    const canDeleteRate = (rateID: string): boolean => {
        if (!customUnit?.customUnitID || !allTransactions) {
            return true;
        }
        return !hasPerDiemRateTransactions(allTransactions, customUnit.customUnitID, rateID);
    };

    const canDeleteSubRate = (rateID: string, subRateID: string): boolean => {
        if (!customUnit?.customUnitID || !allTransactions) {
            return true;
        }
        return !hasPerDiemSubRateTransactions(allTransactions, customUnit.customUnitID, rateID, subRateID);
    };

    // Example 5: Validate individual transactions
    const validateTransaction = (transactionID: string): boolean => {
        if (!allTransactions || !customUnit?.customUnitID) {
            return false;
        }
        
        const transaction = allTransactions[transactionID];
        if (!transaction) {
            return false;
        }

        return validatePerDiemTransactionEligibility(
            transaction,
            customUnit.customUnitID,
            specificRateID,
            specificSubRateID
        );
    };

    // Example usage in component logic
    const eligibleTransactionIDs = eligibleTransactionsData?.transactionIDs;
    const rateIDToTransactionIDsMap = eligibleTransactionsData?.rateIDToTransactionIDsMap;
    const subRateIDToTransactionIDsMap = eligibleTransactionsData?.subRateIDToTransactionIDsMap;

    const hasViolations = !!transactionViolations && Object.keys(transactionViolations).length > 0;
    const hasTransactions = eligibleTransactionIDs && eligibleTransactionIDs.size > 0;

    // Example: Get transaction count for a specific rate
    const getTransactionCountForRate = (rateID: string): number => {
        return rateIDToTransactionIDsMap?.[rateID]?.length ?? 0;
    };

    // Example: Get transaction count for a specific subrate
    const getTransactionCountForSubRate = (subRateID: string): number => {
        return subRateIDToTransactionIDsMap?.[subRateID]?.length ?? 0;
    };

    // Example: Filter rates that have transactions
    const ratesWithTransactions = Object.values(customUnitRates).filter((rate) => 
        getTransactionCountForRate(rate.customUnitRateID) > 0
    );

    // Example: Get all subrates with transactions
    const subRatesWithTransactions = Object.values(customUnitRates)
        .flatMap((rate) => 
            (rate.subRates ?? []).filter((subRate) => 
                getTransactionCountForSubRate(subRate.id) > 0
            ).map((subRate) => ({
                ...subRate,
                rateID: rate.customUnitRateID,
                rateName: rate.name,
            }))
        );

    return (
        <div>
            <h2>Per Diem Validation Example</h2>
            
            <div>
                <h3>Transaction Statistics</h3>
                <p>Total eligible transactions: {eligibleTransactionIDs?.size ?? 0}</p>
                <p>Has violations: {hasViolations ? 'Yes' : 'No'}</p>
                <p>Has transactions: {hasTransactions ? 'Yes' : 'No'}</p>
            </div>

            <div>
                <h3>Rates with Transactions</h3>
                <ul>
                    {ratesWithTransactions.map((rate) => (
                        <li key={rate.customUnitRateID}>
                            {rate.name}: {getTransactionCountForRate(rate.customUnitRateID)} transactions
                            (Can delete: {canDeleteRate(rate.customUnitRateID) ? 'Yes' : 'No'})
                        </li>
                    ))}
                </ul>
            </div>

            <div>
                <h3>SubRates with Transactions</h3>
                <ul>
                    {subRatesWithTransactions.map((subRate) => (
                        <li key={subRate.id}>
                            {subRate.rateName} - {subRate.name}: {getTransactionCountForSubRate(subRate.id)} transactions
                            (Can delete: {canDeleteSubRate(subRate.rateID, subRate.id) ? 'Yes' : 'No'})
                        </li>
                    ))}
                </ul>
            </div>

            <div>
                <h3>Specific Rate/SubRate Transactions</h3>
                <p>
                    Transactions for rate {specificRateID} and subrate {specificSubRateID}: 
                    {specificEligibleTransactionIDs?.size ?? 0}
                </p>
            </div>
        </div>
    );
}

export default PerDiemValidationExample;

/**
 * Key Differences Between Distance Rate and Per Diem Validation:
 * 
 * 1. STRUCTURE COMPLEXITY:
 *    - Distance Rate: Single level (customUnitID + customUnitRateID)
 *    - Per Diem: Two levels (customUnitID + customUnitRateID + subRates[])
 * 
 * 2. TRANSACTION FILTERING:
 *    - Distance Rate: Filter by customUnitID and customUnitRateID
 *    - Per Diem: Filter by customUnitID, customUnitRateID, AND subRates
 * 
 * 3. VALIDATION REQUIREMENTS:
 *    - Distance Rate: Must have waypoints/distance data
 *    - Per Diem: Must have subRates array and date range
 * 
 * 4. CUSTOM UNIT NAME:
 *    - Distance Rate: CONST.CUSTOM_UNITS.NAME_DISTANCE
 *    - Per Diem: CONST.CUSTOM_UNITS.NAME_PER_DIEM_INTERNATIONAL
 * 
 * 5. DELETION VALIDATION:
 *    - Distance Rate: Check if rate has transactions
 *    - Per Diem: Check if rate OR specific subrate has transactions
 * 
 * 6. MAPPING COMPLEXITY:
 *    - Distance Rate: rateID → transactionIDs
 *    - Per Diem: rateID → transactionIDs AND subRateID → transactionIDs
 */
