name: Build and deploy apps for testing

on:
  workflow_dispatch:
    inputs:
      # If not specified, only build iOS and Android apps from the main branch of Expensify/App
      APP_PULL_REQUEST_NUMBER:
        description: Expensify/App PR number for correct placement of apps. Default to main.
        required: false
        default: ''
      # Pull Request number from Mobile-Expensify repo for correct placement of OD app. It will take precedence over MOBILE-EXPENSIFY from App's PR description if both are specified. If nothing is specified defaults to Mobile-Expensify's main
      MOBILE_EXPENSIFY_PULL_REQUEST_NUMBER:
        description: Expensify/Mobile-Expensify PR number. Defaults to main. Overrides MOBILE-EXPENSIFY set in <PERSON><PERSON>'s PR description.
        required: false
        default: ''
      REVIEWED_CODE:
        description: I reviewed this pull request and verified that it does not contain any malicious code.
        type: boolean
        required: true
        default: false
      WEB:
        description: Should build web app?
        type: boolean
        default: true
      DESKTOP:
        description: Should build desktop app?
        type: boolean
        default: true
      IOS:
        description: Should build iOS app?
        type: boolean
        default: true
      ANDROID:
        description: Should build android app?
        type: boolean
        default: true

env:
  # This variable is needed for fastlane to construct correct path
  PULL_REQUEST_NUMBER: ${{ inputs.APP_PULL_REQUEST_NUMBER || github.event.number }}

jobs:
  prep:
    runs-on: ubuntu-latest
    outputs:
      APP_REF: ${{ steps.getHeadRef.outputs.REF || 'main' }}
    steps:
      - name: Checkout
        # v4
        uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608

      - name: Validate that user is an Expensify employee
        uses: ./.github/actions/composite/validateActor
        with:
          REQUIRE_APP_DEPLOYER: false
          OS_BOTIFY_TOKEN: ${{ secrets.OS_BOTIFY_COMMIT_TOKEN }}

      - name: Validate that the user reviewed the pull request before running a test build
        if: ${{ !inputs.REVIEWED_CODE }}
        run: |
          echo "::error::🕵️‍♀️ Please carefully review the pull request before running a test build to ensure it does not contain any malicious code"
          exit 1

      - name: Check if App pull request number is correct
        if: ${{ github.event_name == 'workflow_dispatch' }}
        id: getHeadRef
        run: |
          set -e
          if [ -z "${{ inputs.APP_PULL_REQUEST_NUMBER }}" ]; then
            echo "REF=" >> "$GITHUB_OUTPUT"
          else
            echo "REF=$(gh pr view ${{ inputs.APP_PULL_REQUEST_NUMBER }} --json headRefOid --jq '.headRefOid')" >> "$GITHUB_OUTPUT"
          fi
        env:
          GITHUB_TOKEN: ${{ github.token }}
    
  ********************:
    runs-on: ubuntu-latest
    needs: [prep]
    outputs:
      MOBILE_EXPENSIFY_PR: ${{ steps.mobileExpensifyPR.outputs.result }}
    steps:
      - name: Check if author specified Expensify/Mobile-Expensify PR
        id: mobileExpensifyPR
        # v7
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea
        with:
          github-token: ${{ github.token }}
          result-encoding: string
          script: |
            if ('${{ inputs.MOBILE_EXPENSIFY_PULL_REQUEST_NUMBER}}') return '${{ inputs.MOBILE_EXPENSIFY_PULL_REQUEST_NUMBER}}';

            if (!'${{ inputs.APP_PULL_REQUEST_NUMBER }}') return '';

            const pullRequest = await github.rest.pulls.get({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: '${{ inputs.APP_PULL_REQUEST_NUMBER }}',
            });
            
            const body = pullRequest.data.body;
            const regex = /MOBILE-EXPENSIFY:\s*https:\/\/github.com\/Expensify\/Mobile-Expensify\/pull\/(?<prNumber>\d+)/;
            const found = body.match(regex)?.groups?.prNumber || "";

            return found.trim();

  getMobileExpensifyRef:
      runs-on: ubuntu-latest
      needs: [********************]
      outputs:
        MOBILE_EXPENSIFY_REF: ${{ steps.getHeadRef.outputs.REF || 'main' }}
      steps:
        - name: Checkout
          # v4
          uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608

        - name: Check if Expensify/Mobile-Expensify pull request number is correct
          id: getHeadRef
          run: |
            set -e
            if [[ -z "${{ inputs.MOBILE_EXPENSIFY_PULL_REQUEST_NUMBER }}" && -z "${{ needs.********************.outputs.MOBILE_EXPENSIFY_PR }}" ]]; then
              echo "REF=" >> "$GITHUB_OUTPUT"
            else
              echo "PR=${{ inputs.MOBILE_EXPENSIFY_PULL_REQUEST_NUMBER || needs.********************.outputs.MOBILE_EXPENSIFY_PR }}" >> "$GITHUB_OUTPUT"
              echo "REF=$(gh pr view ${{ inputs.MOBILE_EXPENSIFY_PULL_REQUEST_NUMBER || needs.********************.outputs.MOBILE_EXPENSIFY_PR }} -R Expensify/Mobile-Expensify --json headRefOid --jq '.headRefOid')" >> "$GITHUB_OUTPUT"
            fi
          env:
            GITHUB_TOKEN: ${{ secrets.OS_BOTIFY_TOKEN }}

  postGitHubCommentBuildStarted:
    name: Post build started comment
    runs-on: ubuntu-latest
    needs: [prep, ********************, getMobileExpensifyRef]
    steps:
      - name: Add build start comment to Expensify/App PR
        if: ${{ inputs.APP_PULL_REQUEST_NUMBER != ''}}
        # v7
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea
        with:
          github-token: ${{ github.token }}
          script: |
              const workflowURL = `https://github.com/${context.repo.owner}/${context.repo.repo}/actions/runs/${context.runId}`;
              github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: ${{ inputs.APP_PULL_REQUEST_NUMBER }},
              body: `🚧 @${{ github.actor }} has triggered a test Expensify/App build. You can view the [workflow run here](${workflowURL}).`
              });

      - name: Add build start comment to Expensify/Mobile-Expensify PR
        if: ${{ needs.********************.outputs.MOBILE_EXPENSIFY_PR != '' }}
        # v7
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea
        with:
          github-token: ${{ secrets.OS_BOTIFY_TOKEN }}
          script: |
              const workflowURL = `https://github.com/${context.repo.owner}/${context.repo.repo}/actions/runs/${context.runId}`;
              github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: 'Mobile-Expensify',
              issue_number: ${{ needs.********************.outputs.MOBILE_EXPENSIFY_PR }},
              body: `🚧 @${{ github.actor }} has triggered a test Expensify/Mobile-Expensify build. You can view the [workflow run here](${workflowURL}).`
              });

  desktop:
    name: Build and deploy Desktop for testing
    if: ${{ inputs.DESKTOP && inputs.APP_PULL_REQUEST_NUMBER }}
    needs: [prep]
    runs-on: macos-14-large
    steps:
      - name: Checkout
        # v4
        uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608
        with:
          ref: ${{ needs.prep.outputs.APP_REF }}

      - name: Create .env.adhoc file based on staging and add PULL_REQUEST_NUMBER env to it
        run: |
          cp .env.staging .env.adhoc
          sed -i '' 's/ENVIRONMENT=staging/ENVIRONMENT=adhoc/' .env.adhoc
          echo "PULL_REQUEST_NUMBER=$PULL_REQUEST_NUMBER" >> .env.adhoc

      - name: Setup Node
        uses: ./.github/actions/composite/setupNode
        with:
          IS_DESKTOP_BUILD: true

      - name: Load Desktop credentials from 1Password
        id: load-credentials
        # v2
        uses: 1password/load-secrets-action@581a835fb51b8e7ec56b71cf2ffddd7e68bb25e0
        with:
          export-env: false
        env:
          OP_SERVICE_ACCOUNT_TOKEN: ${{ secrets.OP_SERVICE_ACCOUNT_TOKEN }}
          DESKTOP_CERTIFICATE_BASE64: "op://${{ vars.OP_VAULT }}/Desktop Certificates.p12/CSC_LINK"
          DESKTOP_CERTIFICATE_PASSWORD: "op://${{ vars.OP_VAULT }}/Desktop Certificates.p12/CSC_KEY_PASSWORD"

      - name: Configure AWS Credentials
        # v4
        uses: aws-actions/configure-aws-credentials@ececac1a45f3b08a01d2dd070d28d111c5fe6722
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Build desktop app for testing
        run: npm run desktop-build-adhoc
        env:
          CSC_LINK: ${{ steps.load-credentials.outputs.DESKTOP_CERTIFICATE_BASE64 }}
          CSC_KEY_PASSWORD: ${{ steps.load-credentials.outputs.DESKTOP_CERTIFICATE_PASSWORD }}
          APPLE_ID: ${{ secrets.APPLE_ID }}
          APPLE_TEAM_ID: ${{ secrets.APPLE_TEAM_ID }}
          APPLE_APP_SPECIFIC_PASSWORD: ${{ secrets.APPLE_ID_PASSWORD }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          GCP_GEOLOCATION_API_KEY: ${{ secrets.GCP_GEOLOCATION_API_KEY_STAGING }}

  web:
    name: Build and deploy Web
    if: ${{ inputs.WEB && inputs.APP_PULL_REQUEST_NUMBER }}
    needs: [prep]
    runs-on: ubuntu-latest-xl
    steps:
      - name: Checkout
        # v4
        uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608
        with:
          ref: ${{ needs.prep.outputs.APP_REF }}

      - name: Create .env.adhoc file based on staging and add PULL_REQUEST_NUMBER env to it
        run: |
          cp .env.staging .env.adhoc
          sed -i 's/ENVIRONMENT=staging/ENVIRONMENT=adhoc/' .env.adhoc
          echo "PULL_REQUEST_NUMBER=$PULL_REQUEST_NUMBER" >> .env.adhoc

      - name: Setup Node
        uses: ./.github/actions/composite/setupNode

      - name: Configure AWS Credentials
        # v4
        uses: aws-actions/configure-aws-credentials@ececac1a45f3b08a01d2dd070d28d111c5fe6722
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Build web for testing
        run: npm run build-adhoc

      - name: Deploy to S3 for internal testing
        run: aws s3 cp --recursive --acl public-read "$GITHUB_WORKSPACE"/dist s3://ad-hoc-expensify-cash/web/"$PULL_REQUEST_NUMBER"

  androidHybrid:
    name: Build Android HybridApp
    if: ${{ inputs.ANDROID }}
    needs: [prep, ********************, getMobileExpensifyRef]
    runs-on: ubuntu-latest-xl
    outputs:
      S3_APK_PATH: ${{ steps.exportAndroidS3Path.outputs.S3_APK_PATH }}
    steps:
      - name: Checkout
        # v4
        uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608
        with:
          submodules: true
          ref: ${{ needs.prep.outputs.APP_REF }}
          token: ${{ secrets.OS_BOTIFY_TOKEN }}

      - name: Checkout Mobile-Expensify to specified branch or commit
        if: ${{ needs.getMobileExpensifyRef.outputs.MOBILE_EXPENSIFY_REF != '' }}
        run: |
          cd Mobile-Expensify
          git fetch origin ${{ needs.getMobileExpensifyRef.outputs.MOBILE_EXPENSIFY_REF }}
          git checkout ${{ needs.getMobileExpensifyRef.outputs.MOBILE_EXPENSIFY_REF }}
          echo "Building from https://github.com/Expensify/Mobile-Expensify/pull/${{ needs.********************.outputs.MOBILE_EXPENSIFY_PR }}"

      - name: Configure MapBox SDK
        run: ./scripts/setup-mapbox-sdk.sh ${{ secrets.MAPBOX_SDK_DOWNLOAD_TOKEN }}

      - name: Setup Node
        id: setup-node
        uses: ./.github/actions/composite/setupNode
        with:
          IS_HYBRID_BUILD: 'true'

      - name: Run grunt build
        run: |
            cd Mobile-Expensify
            npm run grunt:build:shared

      - name: Setup dotenv
        run: |
            cp .env.staging .env.adhoc
            sed -i 's/ENVIRONMENT=staging/ENVIRONMENT=adhoc/' .env.adhoc
            echo "APP_PULL_REQUEST_NUMBER=$PULL_REQUEST_NUMBER" >> .env.adhoc

      - name: Setup Java
        # v4
        uses: actions/setup-java@3a4f6e1af504cf6a31855fa899c6aa5355ba6c12
        with:
          distribution: 'oracle'
          java-version: '17'

      - name: Setup Ruby
        # v1.229.0
        uses: ruby/setup-ruby@354a1ad156761f5ee2b7b13fa8e09943a5e8d252
        with:
          bundler-cache: true

      - name: Install 1Password CLI
        # v1
        uses: 1password/install-cli-action@143a85f84a90555d121cde2ff5872e393a47ab9f

      - name: Load files from 1Password
        env:
          OP_SERVICE_ACCOUNT_TOKEN: ${{ secrets.OP_SERVICE_ACCOUNT_TOKEN }}
        run: |
          op read "op://${{ vars.OP_VAULT }}/upload-key.keystore/upload-key.keystore" --force --out-file ./upload-key.keystore
          op read "op://${{ vars.OP_VAULT }}/android-fastlane-json-key.json/android-fastlane-json-key.json" --force --out-file ./android-fastlane-json-key.json

          # Copy the keystore to the Android directory for Fullstory
          cp ./upload-key.keystore Mobile-Expensify/Android

      - name: Load Android upload keystore credentials from 1Password
        id: load-credentials
        # v2
        uses: 1password/load-secrets-action@581a835fb51b8e7ec56b71cf2ffddd7e68bb25e0
        with:
          export-env: false
        env:
          OP_SERVICE_ACCOUNT_TOKEN: ${{ secrets.OP_SERVICE_ACCOUNT_TOKEN }}
          ANDROID_UPLOAD_KEYSTORE_PASSWORD: op://${{ vars.OP_VAULT }}/Repository-Secrets/ANDROID_UPLOAD_KEYSTORE_PASSWORD
          ANDROID_UPLOAD_KEYSTORE_ALIAS: op://${{ vars.OP_VAULT }}/Repository-Secrets/ANDROID_UPLOAD_KEYSTORE_ALIAS
          ANDROID_UPLOAD_KEY_PASSWORD: op://${{ vars.OP_VAULT }}/Repository-Secrets/ANDROID_UPLOAD_KEY_PASSWORD

      - name: Build Android app
        id: build
        env:
          ANDROID_UPLOAD_KEYSTORE_PASSWORD: ${{ steps.load-credentials.outputs.ANDROID_UPLOAD_KEYSTORE_PASSWORD }}
          ANDROID_UPLOAD_KEYSTORE_ALIAS: ${{ steps.load-credentials.outputs.ANDROID_UPLOAD_KEYSTORE_ALIAS }}
          ANDROID_UPLOAD_KEY_PASSWORD: ${{ steps.load-credentials.outputs.ANDROID_UPLOAD_KEY_PASSWORD }}
        run: bundle exec fastlane android build_adhoc_hybrid

      - name: Configure AWS Credentials
        # v4
        uses: aws-actions/configure-aws-credentials@ececac1a45f3b08a01d2dd070d28d111c5fe6722
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Upload Android AdHoc build to S3
        run: bundle exec fastlane android upload_s3
        env:
          S3_ACCESS_KEY: ${{ secrets.AWS_ACCESS_KEY_ID }}
          S3_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          S3_BUCKET: ad-hoc-expensify-cash
          S3_REGION: us-east-1

      - name: Export S3 path
        id: exportAndroidS3Path
        run: |
          # $s3APKPath is set from within the Fastfile, android upload_s3 lane
          echo "S3_APK_PATH=$s3APKPath" >> "$GITHUB_OUTPUT"

  iosHybrid:
    name: Build and deploy iOS for testing
    if: ${{ inputs.IOS }}
    needs: [prep, ********************, getMobileExpensifyRef]
    env:
      DEVELOPER_DIR: /Applications/Xcode_16.4.0.app/Contents/Developer
    runs-on: macos-15-xlarge
    outputs:
      IOS_PATH: ${{ steps.export-ios-path.outputs.IOS_PATH }}
    steps:
      - name: Checkout
        # v4
        uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608
        with:
          submodules: true
          ref: ${{ needs.prep.outputs.APP_REF }}
          token: ${{ secrets.OS_BOTIFY_TOKEN }}

      - name: Checkout Mobile-Expensify to specified branch or commit
        if: ${{ needs.getMobileExpensifyRef.outputs.MOBILE_EXPENSIFY_REF != '' }}
        run: |
          cd Mobile-Expensify
          git fetch origin ${{ needs.getMobileExpensifyRef.outputs.MOBILE_EXPENSIFY_REF }}
          git checkout ${{ needs.getMobileExpensifyRef.outputs.MOBILE_EXPENSIFY_REF }}
          echo "Building from https://github.com/Expensify/Mobile-Expensify/pull/${{ needs.********************.outputs.MOBILE_EXPENSIFY_PR }}"

      - name: Configure MapBox SDK
        run: ./scripts/setup-mapbox-sdk.sh ${{ secrets.MAPBOX_SDK_DOWNLOAD_TOKEN }}

      - name: Setup Node
        id: setup-node
        uses: ./.github/actions/composite/setupNode
        with:
          IS_HYBRID_BUILD: 'true'

      - name: Create .env.adhoc file based on staging and add PULL_REQUEST_NUMBER env to it
        run: |
          cp .env.staging .env.adhoc
          sed -i '' 's/ENVIRONMENT=staging/ENVIRONMENT=adhoc/' .env.adhoc
          echo "PULL_REQUEST_NUMBER=$PULL_REQUEST_NUMBER" >> .env.adhoc

      - name: Setup Ruby
        # v1.229.0
        uses: ruby/setup-ruby@354a1ad156761f5ee2b7b13fa8e09943a5e8d252
        with:
          bundler-cache: true

      - name: Install New Expensify Gems
        run: bundle install

      - name: Cache Pod dependencies
        # v4
        uses: actions/cache@1bd1e32a3bdc45362d1e726936510720a7c30a57
        id: pods-cache
        with:
          path: Mobile-Expensify/iOS/Pods
          key: ${{ runner.os }}-pods-cache-${{ hashFiles('Mobile-Expensify/iOS/Podfile.lock', 'firebase.json') }}

      - name: Compare Podfile.lock and Manifest.lock
        id: compare-podfile-and-manifest
        run: echo "IS_PODFILE_SAME_AS_MANIFEST=${{ hashFiles('Mobile-Expensify/iOS/Podfile.lock') == hashFiles('Mobile-Expensify/iOS/Manifest.lock') }}" >> "$GITHUB_OUTPUT"

      - name: Install cocoapods
        uses: nick-fields/retry@3f757583fb1b1f940bc8ef4bf4734c8dc02a5847
        if: steps.pods-cache.outputs.cache-hit != 'true' || steps.compare-podfile-and-manifest.outputs.IS_PODFILE_SAME_AS_MANIFEST != 'true' || steps.setup-node.outputs.cache-hit != 'true'
        with:
          timeout_minutes: 10
          max_attempts: 5
          command: npm run pod-install

      - name: Install 1Password CLI
        # v1
        uses: 1password/install-cli-action@143a85f84a90555d121cde2ff5872e393a47ab9f

      - name: Load files from 1Password
        env:
          OP_SERVICE_ACCOUNT_TOKEN: ${{ secrets.OP_SERVICE_ACCOUNT_TOKEN }}
        run: |
          op read "op://${{ vars.OP_VAULT }}/OldApp_AdHoc/OldApp_AdHoc.mobileprovision" --force --out-file ./OldApp_AdHoc.mobileprovision
          op read "op://${{ vars.OP_VAULT }}/OldApp_AdHoc_Share_Extension/OldApp_AdHoc_Share_Extension.mobileprovision" --force --out-file ./OldApp_AdHoc_Share_Extension.mobileprovision
          op read "op://${{ vars.OP_VAULT }}/OldApp_AdHoc_Notification_Service/OldApp_AdHoc_Notification_Service.mobileprovision" --force --out-file ./OldApp_AdHoc_Notification_Service.mobileprovision
          op read "op://${{ vars.OP_VAULT }}/New Expensify Distribution Certificate/Certificates.p12" --force --out-file ./Certificates.p12

      - name: Build AdHoc app
        run: bundle exec fastlane ios build_adhoc_hybrid

      - name: Configure AWS Credentials
        # v4
        uses: aws-actions/configure-aws-credentials@ececac1a45f3b08a01d2dd070d28d111c5fe6722
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Upload AdHoc build to S3
        run: bundle exec fastlane ios upload_s3
        env:
          S3_ACCESS_KEY: ${{ secrets.AWS_ACCESS_KEY_ID }}
          S3_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          S3_BUCKET: ad-hoc-expensify-cash
          S3_REGION: us-east-1

      - name: Export iOS path
        id: export-ios-path
        run: |
          content_ios="$(cat ./ios_paths.json)"
          content_ios="${content_ios//'%'/'%25'}"
          content_ios="${content_ios//$'\n'/'%0A'}"
          content_ios="${content_ios//$'\r'/'%0D'}"
          ios_path=$(echo "$content_ios" | jq -r '.html_path')
          echo "IOS_PATH=$ios_path" >> "$GITHUB_OUTPUT"

  postGithubComment:
    runs-on: ubuntu-latest
    if: always()
    name: Post a GitHub comment with app download links for testing
    needs: [prep, ********************, desktop, web, androidHybrid, iosHybrid]
    steps:
      - name: Checkout
        # v4
        uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608
        with:
          ref: ${{ needs.prep.outputs.APP_REF }}

      - name: Download Artifact
        # v4
        uses: actions/download-artifact@95815c38cf2ff2164869cbab79da8d1f422bc89e

      - name: Publish links to apps for download on Expensify/App PR
        if: ${{ inputs.APP_PULL_REQUEST_NUMBER || (!inputs.APP_PULL_REQUEST_NUMBER && !needs.********************.outputs.MOBILE_EXPENSIFY_PR) }}
        uses: ./.github/actions/javascript/postTestBuildComment
        with:
          REPO: App
          APP_PR_NUMBER: ${{ inputs.APP_PULL_REQUEST_NUMBER }}
          MOBILE_EXPENSIFY_PR_NUMBER: ${{ needs.********************.outputs.MOBILE_EXPENSIFY_PR }}
          GITHUB_TOKEN: ${{ github.token }}
          ANDROID: ${{ needs.androidHybrid.result }}
          DESKTOP: ${{ needs.desktop.result }}
          IOS: ${{ needs.iosHybrid.result }}
          WEB: ${{ needs.web.result }}
          ANDROID_LINK: ${{ needs.androidHybrid.outputs.S3_APK_PATH }}
          DESKTOP_LINK: https://ad-hoc-expensify-cash.s3.amazonaws.com/desktop/${{ inputs.APP_PULL_REQUEST_NUMBER }}/NewExpensify.dmg
          IOS_LINK: ${{ needs.iosHybrid.outputs.IOS_PATH }}
          WEB_LINK: https://${{ inputs.APP_PULL_REQUEST_NUMBER }}.pr-testing.expensify.com

      - name: Publish links to apps for download on Expensify/Mobile-Expensify PR
        if: ${{ needs.********************.outputs.MOBILE_EXPENSIFY_PR }}
        uses: ./.github/actions/javascript/postTestBuildComment
        with:
          REPO: Mobile-Expensify
          MOBILE_EXPENSIFY_PR_NUMBER: ${{ needs.********************.outputs.MOBILE_EXPENSIFY_PR }}
          GITHUB_TOKEN: ${{ secrets.OS_BOTIFY_TOKEN }}
          ANDROID: ${{ needs.androidHybrid.result }}
          IOS: ${{ needs.iosHybrid.result }}
          ANDROID_LINK: ${{ needs.androidHybrid.outputs.S3_APK_PATH }}
          IOS_LINK: ${{ needs.iosHybrid.outputs.IOS_PATH }}
