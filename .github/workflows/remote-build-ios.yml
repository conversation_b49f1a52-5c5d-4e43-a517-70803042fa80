name: Remote Build iOS

on:
  workflow_dispatch:
  push:
    branches-ignore: [staging, production]
    paths-ignore: ['docs/**', 'contributingGuides/**', 'help/**', '.github/**', 'scripts/**', 'tests/**']

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

jobs:
  build:
    runs-on: ${{ github.repository_owner == 'Expensify' && 'macos-15-xlarge' || 'macos-15' }}
    env:
      DEVELOPER_DIR: /Applications/Xcode_16.4.0.app/Contents/Developer
    strategy:
      fail-fast: false
      matrix:
        include:
          - scheme: 'New Expensify Dev'
            configuration: 'DebugDevelopment'
            is_hybrid_build: false

          - scheme: 'Expensify Dev'
            configuration: 'Debug'
            is_hybrid_build: true
    steps:
      - name: Checkout
        # v4
        uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608
        with:
          submodules: ${{ matrix.is_hybrid_build || false }}
          token: ${{ secrets.OS_BOTIFY_TOKEN }}

      - name: Setup Node
        uses: ./.github/actions/composite/setupNode
        with:
          IS_HYBRID_BUILD: ${{ matrix.is_hybrid_build && 'true' || 'false' }}

      - name: RNEF Remote Build - iOS
        # rnef v2
        uses: callstackincubator/ios@7847e0676242cefa801cba8ede6736140b7bae91 
        env:
          GITHUB_TOKEN: ${{ github.token }}
          IS_HYBRID_APP: ${{ matrix.is_hybrid_build }}
        with:
          destination: simulator
          scheme: ${{ matrix.scheme }}
          configuration: ${{ matrix.configuration }}
          comment-bot: false
