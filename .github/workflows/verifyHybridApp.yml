name: Verify HybridApp build

on:
  pull_request:
    types: [opened, synchronize]
    branches-ignore: [staging, production]
    paths:
      - '**.kt'
      - '**.java'
      - '**.swift'
      - '**.mm'
      - '**.h'
      - '**.cpp'
      - 'package.json'
      - 'patches/**'
      - 'android/build.gradle'
      - 'android/AndroidManifest.xml'
      - 'ios/Podfile.lock'
      - 'ios/project.pbxproj'
  pull_request_target:
    types: [opened, synchronize]
    branches: [main]
    paths:
      - '**.kt'
      - '**.java'
      - '**.swift'
      - '**.mm'
      - '**.h'
      - '**.cpp'
      - 'package.json'
      - 'patches/**'
      - 'android/build.gradle'
      - 'android/AndroidManifest.xml'
      - 'ios/Podfile.lock'
      - 'ios/project.pbxproj'

permissions:
  pull-requests: write
  contents: read

concurrency:
  group: ${{ github.ref == 'refs/heads/main' && format('{0}-{1}', github.ref, github.sha) || github.ref }}-verify-main
  cancel-in-progress: true

jobs:
  comment_on_fork:
    name: Comment on all PRs that are forks
    # Only run on pull requests that *are* a fork
    if: ${{ github.event.pull_request.head.repo.fork && github.event_name == 'pull_request_target' }}
    runs-on: ubuntu-latest
    steps:
      - name: Comment on forks
        run: |
          PR_URL="${{ github.event.pull_request.html_url }}"
          COMMENT=":warning: This PR is possibly changing native code and/or updating libraries, it may cause problems with HybridApp. \
          Please check if any patch updates are required in the HybridApp repo and run an AdHoc build to verify that HybridApp will not break. \
          Ask Contributor Plus for help if you are not sure how to handle this. :warning:"

          # Fetch existing comments from author github-actions
          EXISTING_COMMENTS="$(\
            gh pr view "$PR_URL" \
              --comments \
              --json comments \
              --jq '.comments[] | select(.author = "github-actions") | .body' \
          )"

          # Leave the comment only if it's not already present
          if grep -q "$COMMENT" <(echo "$EXISTING_COMMENTS"); then
            echo "Comment already exists, not posting again."
          else
            gh pr comment "$PR_URL" --body "$COMMENT"
          fi
        env:
          GITHUB_TOKEN: ${{ github.token }}
  verify_android:
    name: Verify Android HybridApp builds on main
    runs-on: ubuntu-latest-xl
    # Only run on pull requests that are *not* on a fork
    if: ${{ !github.event.pull_request.head.repo.fork && github.event_name == 'pull_request' }}
    steps:
      - name: Checkout
        # v4
        uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608
        with:
          submodules: true
          ref: ${{ github.event.pull_request.head.sha }}
          token: ${{ secrets.OS_BOTIFY_TOKEN }}

      - name: Configure MapBox SDK
        run: ./scripts/setup-mapbox-sdk.sh ${{ secrets.MAPBOX_SDK_DOWNLOAD_TOKEN }}

      - name: Setup Node
        id: setup-node
        uses: ./.github/actions/composite/setupNode
        with:
          IS_HYBRID_BUILD: 'true'

      - name: Setup Ruby
        # v1.229.0
        uses: ruby/setup-ruby@354a1ad156761f5ee2b7b13fa8e09943a5e8d252
        with:
          bundler-cache: true

      - name: Build Android Debug
        run: |
          if ! npm run android-hybrid-build
          then
            echo "❌ Android HybridApp failed to build: Please reach out to Contributor+ and/or Expensify engineers for help in #expensify-open-source to resolve."
            exit 1
          fi

  verify_ios:
    name: Verify iOS HybridApp builds on main
    runs-on: macos-15-xlarge
    env:
      DEVELOPER_DIR: /Applications/Xcode_16.4.0.app/Contents/Developer
    # Only run on pull requests that are *not* on a fork
    if: ${{ !github.event.pull_request.head.repo.fork && github.event_name == 'pull_request' }}
    steps:
      - name: Checkout
        # v4
        uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608
        with:
          submodules: true
          ref: ${{ github.event.pull_request.head.sha }}
          token: ${{ secrets.OS_BOTIFY_TOKEN }}

      - name: Configure MapBox SDK
        run: ./scripts/setup-mapbox-sdk.sh ${{ secrets.MAPBOX_SDK_DOWNLOAD_TOKEN }}

      - name: Setup Node
        id: setup-node
        uses: ./.github/actions/composite/setupNode
        with:
          IS_HYBRID_BUILD: 'true'

      - name: Setup Ruby
        # v1.229.0
        uses: ruby/setup-ruby@354a1ad156761f5ee2b7b13fa8e09943a5e8d252
        with:
          bundler-cache: true

      - name: Cache Pod dependencies
        # v4
        uses: actions/cache@1bd1e32a3bdc45362d1e726936510720a7c30a57
        id: pods-cache
        with:
          path: Mobile-Expensify/iOS/Pods
          key: ${{ runner.os }}-pods-cache-${{ hashFiles('Mobile-Expensify/iOS/Podfile.lock', 'firebase.json') }}

      - name: Compare Podfile.lock and Manifest.lock
        id: compare-podfile-and-manifest
        run: echo "IS_PODFILE_SAME_AS_MANIFEST=${{ hashFiles('Mobile-Expensify/iOS/Podfile.lock') == hashFiles('Mobile-Expensify/iOS/Manifest.lock') }}" >> "$GITHUB_OUTPUT"

      - name: Install cocoapods
        uses: nick-fields/retry@3f757583fb1b1f940bc8ef4bf4734c8dc02a5847
        if: steps.pods-cache.outputs.cache-hit != 'true' || steps.compare-podfile-and-manifest.outputs.IS_PODFILE_SAME_AS_MANIFEST != 'true' || steps.setup-node.outputs.cache-hit != 'true'
        with:
          timeout_minutes: 10
          max_attempts: 5
          command: npm run pod-install

      - name: Build iOS HybridApp
        run: |
          # Let us know if the builds fails
          set -o pipefail
          
          # Do not start metro
          export RCT_NO_LAUNCH_PACKAGER=1
          
          # Build iOS using xcodebuild
          if ! npm run ios-hybrid-build
          then
            echo "❌ iOS HybridApp failed to build: Please reach out to Contributor+ and/or Expensify engineers for help in #expensify-open-source to resolve."
            exit 1
          fi
