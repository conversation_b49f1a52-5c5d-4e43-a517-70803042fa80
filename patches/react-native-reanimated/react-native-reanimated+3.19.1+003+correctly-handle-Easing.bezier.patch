diff --git a/node_modules/react-native-reanimated/lib/module/layoutReanimation/web/Easing.web.js b/node_modules/react-native-reanimated/lib/module/layoutReanimation/web/Easing.web.js
index ed0f9d3..6baf136 100644
--- a/node_modules/react-native-reanimated/lib/module/layoutReanimation/web/Easing.web.js
+++ b/node_modules/react-native-reanimated/lib/module/layoutReanimation/web/Easing.web.js
@@ -14,4 +14,18 @@ export const WebEasings = {
 export function getEasingByName(easingName) {
   return `cubic-bezier(${WebEasings[easingName].toString()})`;
 }
-//# sourceMappingURL=Easing.web.js.map
\ No newline at end of file
+export function maybeGetBezierEasing(easing) {
+  if (!('factory' in easing)) {
+    return null;
+  }
+  const easingFactory = easing.factory;
+  if (!('__closure' in easingFactory)) {
+    return null;
+  }
+  const closure = easingFactory.__closure;
+  if (!('Bezier' in closure)) {
+    return null;
+  }
+  return `cubic-bezier(${closure.x1}, ${closure.y1}, ${closure.x2}, ${closure.y2})`;
+}
+
diff --git a/node_modules/react-native-reanimated/lib/module/layoutReanimation/web/componentUtils.js b/node_modules/react-native-reanimated/lib/module/layoutReanimation/web/componentUtils.js
index 7f724c4..ad53a74 100644
--- a/node_modules/react-native-reanimated/lib/module/layoutReanimation/web/componentUtils.js
+++ b/node_modules/react-native-reanimated/lib/module/layoutReanimation/web/componentUtils.js
@@ -10,18 +10,28 @@ import { setElementPosition, snapshots } from "./componentStyle.js";
 import { Animations, TransitionType } from "./config.js";
 import { TransitionGenerator } from "./createAnimation.js";
 import { scheduleAnimationCleanup } from "./domUtils.js";
-import { getEasingByName, WebEasings } from "./Easing.web.js";
+import {
+  getEasingByName,
+  maybeGetBezierEasing,
+  WebEasings
+} from "./Easing.web.js";
 import { prepareCurvedTransition } from "./transition/Curved.web.js";
 function getEasingFromConfig(config) {
   if (!config.easingV) {
     return getEasingByName('linear');
   }
   const easingName = config.easingV[EasingNameSymbol];
-  if (!(easingName in WebEasings)) {
-    logger.warn(`Selected easing is not currently supported on web.`);
+  if (easingName in WebEasings) {
+    return getEasingByName(easingName);
+  }
+  const bezierEasing = maybeGetBezierEasing(config.easingV);
+  if (!bezierEasing) {
+    logger.warn(
+      `Selected easing is not currently supported on web. Using linear easing instead.`
+    );
     return getEasingByName('linear');
   }
-  return getEasingByName(easingName);
+  return bezierEasing;
 }
 function getRandomDelay(maxDelay = 1000) {
   return Math.floor(Math.random() * (maxDelay + 1)) / 1000;
