{"compilerOptions": {"baseUrl": "./", "paths": {"group-ib-fp": ["./src/index"]}, "allowUnreachableCode": false, "allowUnusedLabels": false, "esModuleInterop": true, "importsNotUsedAsValues": "error", "forceConsistentCasingInFileNames": true, "jsx": "react", "lib": ["esnext"], "module": "esnext", "moduleResolution": "node", "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "noImplicitUseStrict": false, "noStrictGenericChecks": false, "noUncheckedIndexedAccess": true, "noUnusedLocals": true, "noUnusedParameters": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "esnext"}}