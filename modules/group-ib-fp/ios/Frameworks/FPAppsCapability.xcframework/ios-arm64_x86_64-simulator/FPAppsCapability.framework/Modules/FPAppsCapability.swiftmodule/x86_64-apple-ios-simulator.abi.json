{"ABIRoot": {"kind": "Root", "name": "FPAppsCapability", "printedName": "FPAppsCapability", "children": [{"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "FPAppsCapability"}, {"kind": "Import", "name": "GIBMobileSdk", "printedName": "GIBMobileSdk", "declKind": "Import", "moduleName": "FPAppsCapability"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "FPAppsCapability"}, {"kind": "Import", "name": "GIBMobileSdk", "printedName": "GIBMobileSdk", "declKind": "Import", "moduleName": "FPAppsCapability"}], "json_format_version": 8}, "ConstValues": [{"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/FPAppsCapability/AppsCapability.swift", "kind": "StringLiteral", "offset": 2074, "length": 16, "value": "\"installed_apps\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/FPAppsCapability/AppsCapability.swift", "kind": "StringLiteral", "offset": 2119, "length": 5, "value": "\"://\""}]}