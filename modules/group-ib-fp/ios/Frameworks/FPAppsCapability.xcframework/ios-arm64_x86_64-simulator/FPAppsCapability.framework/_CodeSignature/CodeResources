<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/FPAppsCapability.h</key>
		<data>
		Z5dEd4cUcu56JnM5uy5fD/+vCXY=
		</data>
		<key>Info.plist</key>
		<data>
		RFp2AkYxDawhLB+Rq7WgtzcDVlk=
		</data>
		<key>Modules/FPAppsCapability.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		HV7oyPQh4PGa+77DhT1feK8Rq6U=
		</data>
		<key>Modules/FPAppsCapability.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		gIEGJOQjQBSY5rKDkrh/7WfuuNs=
		</data>
		<key>Modules/FPAppsCapability.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		wGs68ha3ug7gVOLDWonPi7KKQPE=
		</data>
		<key>Modules/FPAppsCapability.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		GilHhi8EvDKWBoYfKTS9FjHULGc=
		</data>
		<key>Modules/FPAppsCapability.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		EZv7RiSHvwpmlR7njvBPnudpOMo=
		</data>
		<key>Modules/FPAppsCapability.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<data>
		GilHhi8EvDKWBoYfKTS9FjHULGc=
		</data>
		<key>Modules/FPAppsCapability.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		zNECyxwSbbHBhb93Q+OD5yl4L7o=
		</data>
		<key>Modules/FPAppsCapability.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		wGs68ha3ug7gVOLDWonPi7KKQPE=
		</data>
		<key>Modules/FPAppsCapability.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		y+gUA1FtOujWKEVS4YTcleVls9o=
		</data>
		<key>Modules/FPAppsCapability.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		80GlmyaWfvRsHfBbfau5nrhDt44=
		</data>
		<key>Modules/FPAppsCapability.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		y+gUA1FtOujWKEVS4YTcleVls9o=
		</data>
		<key>Modules/FPAppsCapability.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		nw7rnJzf8LyzDbnHYxpD5QzCtPE=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		SUw+ieiL/PtLaQWkHpTQ7oq0694=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		lKKXNpnNbKjWqty7Ovu52zbl6ro=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/FPAppsCapability.h</key>
		<dict>
			<key>hash2</key>
			<data>
			W3WIKomMBPY4Xcym5stj9Lkjkf66/ftJejWJiM1hT+s=
			</data>
		</dict>
		<key>Modules/FPAppsCapability.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			zCsa6q7RQws1BW4cB10sMmxdbbswS8irIasHkusz96g=
			</data>
		</dict>
		<key>Modules/FPAppsCapability.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			vMWcHyXViqy7scrv1uRrCbE0SXSnzI3JUV3cYXH9fdA=
			</data>
		</dict>
		<key>Modules/FPAppsCapability.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			o2oYZoiZxEexNlKrahvZV3L1jPb4HJfzf9cN5r5dBL4=
			</data>
		</dict>
		<key>Modules/FPAppsCapability.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			NdIadHpRQ3ZLaGQDqhbL0p6gY0igo/5jYU9GOkL4G/s=
			</data>
		</dict>
		<key>Modules/FPAppsCapability.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			Q2V2n7w6SuJSEeberDLsPXIrUGE0Uw1r2c4r1BH7Z+0=
			</data>
		</dict>
		<key>Modules/FPAppsCapability.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			NdIadHpRQ3ZLaGQDqhbL0p6gY0igo/5jYU9GOkL4G/s=
			</data>
		</dict>
		<key>Modules/FPAppsCapability.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			hnJTK/r0od9V5l5Ox/oqlYohUdumfh1uqI5vf58O5hg=
			</data>
		</dict>
		<key>Modules/FPAppsCapability.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			o2oYZoiZxEexNlKrahvZV3L1jPb4HJfzf9cN5r5dBL4=
			</data>
		</dict>
		<key>Modules/FPAppsCapability.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			KY863Ql4YNtF6GesYcGTu4BiAlZW2mh5YTYX9us2ccE=
			</data>
		</dict>
		<key>Modules/FPAppsCapability.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			v4yHUkOEVt2e9uKS7hX0plRz8chQSgdhT805DsIcfq0=
			</data>
		</dict>
		<key>Modules/FPAppsCapability.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			KY863Ql4YNtF6GesYcGTu4BiAlZW2mh5YTYX9us2ccE=
			</data>
		</dict>
		<key>Modules/FPAppsCapability.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			jCjyohQQDOsIbm08dnjETz6WJk4hHtR+BZ+bU1wr4/U=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			Q1FkQKuLDIjgaT/09klRKq2hdQ9FO/OCxfJjaePrt1o=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			hdywyE9Hf8tHcs5JqnqHYzJdqK5tXIiDzlLii7f3Bns=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
