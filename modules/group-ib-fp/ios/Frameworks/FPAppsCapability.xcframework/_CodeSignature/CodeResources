<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ios-arm64/FPAppsCapability.framework/FPAppsCapability</key>
		<data>
		2WwFDQ6f98QLmFnjVxReZNYZ048=
		</data>
		<key>ios-arm64/FPAppsCapability.framework/Headers/FPAppsCapability.h</key>
		<data>
		Z5dEd4cUcu56JnM5uy5fD/+vCXY=
		</data>
		<key>ios-arm64/FPAppsCapability.framework/Info.plist</key>
		<data>
		fWhqfN4Fo121lhCXLv20A2GMSv4=
		</data>
		<key>ios-arm64/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo</key>
		<data>
		P+cFoiF+JnlXsYseNB/11BNBqWA=
		</data>
		<key>ios-arm64/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/arm64-apple-ios.abi.json</key>
		<data>
		wGs68ha3ug7gVOLDWonPi7KKQPE=
		</data>
		<key>ios-arm64/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<data>
		BOwKH9+a9UVL1JWQrnl0KJJ85A4=
		</data>
		<key>ios-arm64/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<data>
		0oAiCqMelyLbTyLxJ0LLAleEzZw=
		</data>
		<key>ios-arm64/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<data>
		BOwKH9+a9UVL1JWQrnl0KJJ85A4=
		</data>
		<key>ios-arm64/FPAppsCapability.framework/Modules/module.modulemap</key>
		<data>
		SUw+ieiL/PtLaQWkHpTQ7oq0694=
		</data>
		<key>ios-arm64/FPAppsCapability.framework/PrivacyInfo.xcprivacy</key>
		<data>
		lKKXNpnNbKjWqty7Ovu52zbl6ro=
		</data>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/FPAppsCapability</key>
		<data>
		JvAchsbD4ek9fVQpZg0OXbD8c/s=
		</data>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Headers/FPAppsCapability.h</key>
		<data>
		Z5dEd4cUcu56JnM5uy5fD/+vCXY=
		</data>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Info.plist</key>
		<data>
		RFp2AkYxDawhLB+Rq7WgtzcDVlk=
		</data>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		HV7oyPQh4PGa+77DhT1feK8Rq6U=
		</data>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		gIEGJOQjQBSY5rKDkrh/7WfuuNs=
		</data>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		wGs68ha3ug7gVOLDWonPi7KKQPE=
		</data>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		GilHhi8EvDKWBoYfKTS9FjHULGc=
		</data>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		EZv7RiSHvwpmlR7njvBPnudpOMo=
		</data>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<data>
		GilHhi8EvDKWBoYfKTS9FjHULGc=
		</data>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		wGs68ha3ug7gVOLDWonPi7KKQPE=
		</data>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		y+gUA1FtOujWKEVS4YTcleVls9o=
		</data>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		80GlmyaWfvRsHfBbfau5nrhDt44=
		</data>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		y+gUA1FtOujWKEVS4YTcleVls9o=
		</data>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Modules/module.modulemap</key>
		<data>
		SUw+ieiL/PtLaQWkHpTQ7oq0694=
		</data>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/PrivacyInfo.xcprivacy</key>
		<data>
		lKKXNpnNbKjWqty7Ovu52zbl6ro=
		</data>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/_CodeSignature/CodeResources</key>
		<data>
		tcCKjjcaxJVgNQqZCc4ex15ACqw=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>ios-arm64/FPAppsCapability.framework/FPAppsCapability</key>
		<dict>
			<key>hash</key>
			<data>
			2WwFDQ6f98QLmFnjVxReZNYZ048=
			</data>
			<key>hash2</key>
			<data>
			GAK+KI2+4Z9EySe4/QoKar9eFuQ3o7O4NmfrNJMgZeI=
			</data>
		</dict>
		<key>ios-arm64/FPAppsCapability.framework/Headers/FPAppsCapability.h</key>
		<dict>
			<key>hash</key>
			<data>
			Z5dEd4cUcu56JnM5uy5fD/+vCXY=
			</data>
			<key>hash2</key>
			<data>
			W3WIKomMBPY4Xcym5stj9Lkjkf66/ftJejWJiM1hT+s=
			</data>
		</dict>
		<key>ios-arm64/FPAppsCapability.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			fWhqfN4Fo121lhCXLv20A2GMSv4=
			</data>
			<key>hash2</key>
			<data>
			u5oUY9LUFl+ZOkW2GxtGVl4bmB6LTg7cvXio+PKeJVQ=
			</data>
		</dict>
		<key>ios-arm64/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			P+cFoiF+JnlXsYseNB/11BNBqWA=
			</data>
			<key>hash2</key>
			<data>
			R4tn4UMznwu3CfgH8ktqISpuCz/Y5AE4XIxax/50R9Y=
			</data>
		</dict>
		<key>ios-arm64/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/arm64-apple-ios.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			wGs68ha3ug7gVOLDWonPi7KKQPE=
			</data>
			<key>hash2</key>
			<data>
			o2oYZoiZxEexNlKrahvZV3L1jPb4HJfzf9cN5r5dBL4=
			</data>
		</dict>
		<key>ios-arm64/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			BOwKH9+a9UVL1JWQrnl0KJJ85A4=
			</data>
			<key>hash2</key>
			<data>
			CEw16W4RzfsO1FI0pKppkukcV7qSBu1pkgSeQptojKk=
			</data>
		</dict>
		<key>ios-arm64/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			0oAiCqMelyLbTyLxJ0LLAleEzZw=
			</data>
			<key>hash2</key>
			<data>
			AtrZlSliVwYFFlnzfLDa2GODMHjqXi6wkN3eARrlXJo=
			</data>
		</dict>
		<key>ios-arm64/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			BOwKH9+a9UVL1JWQrnl0KJJ85A4=
			</data>
			<key>hash2</key>
			<data>
			CEw16W4RzfsO1FI0pKppkukcV7qSBu1pkgSeQptojKk=
			</data>
		</dict>
		<key>ios-arm64/FPAppsCapability.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			SUw+ieiL/PtLaQWkHpTQ7oq0694=
			</data>
			<key>hash2</key>
			<data>
			Q1FkQKuLDIjgaT/09klRKq2hdQ9FO/OCxfJjaePrt1o=
			</data>
		</dict>
		<key>ios-arm64/FPAppsCapability.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			lKKXNpnNbKjWqty7Ovu52zbl6ro=
			</data>
			<key>hash2</key>
			<data>
			hdywyE9Hf8tHcs5JqnqHYzJdqK5tXIiDzlLii7f3Bns=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/FPAppsCapability</key>
		<dict>
			<key>hash</key>
			<data>
			JvAchsbD4ek9fVQpZg0OXbD8c/s=
			</data>
			<key>hash2</key>
			<data>
			C5jJVKWvfWT8vweB7NxGcQ8SGGKq+qw2M8MgfrSvoG8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Headers/FPAppsCapability.h</key>
		<dict>
			<key>hash</key>
			<data>
			Z5dEd4cUcu56JnM5uy5fD/+vCXY=
			</data>
			<key>hash2</key>
			<data>
			W3WIKomMBPY4Xcym5stj9Lkjkf66/ftJejWJiM1hT+s=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			RFp2AkYxDawhLB+Rq7WgtzcDVlk=
			</data>
			<key>hash2</key>
			<data>
			woI7hWqfMnJPeAQH+1c5SLZekUq2ftYizzcl2wLRSLk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			HV7oyPQh4PGa+77DhT1feK8Rq6U=
			</data>
			<key>hash2</key>
			<data>
			zCsa6q7RQws1BW4cB10sMmxdbbswS8irIasHkusz96g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			gIEGJOQjQBSY5rKDkrh/7WfuuNs=
			</data>
			<key>hash2</key>
			<data>
			vMWcHyXViqy7scrv1uRrCbE0SXSnzI3JUV3cYXH9fdA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			wGs68ha3ug7gVOLDWonPi7KKQPE=
			</data>
			<key>hash2</key>
			<data>
			o2oYZoiZxEexNlKrahvZV3L1jPb4HJfzf9cN5r5dBL4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			GilHhi8EvDKWBoYfKTS9FjHULGc=
			</data>
			<key>hash2</key>
			<data>
			NdIadHpRQ3ZLaGQDqhbL0p6gY0igo/5jYU9GOkL4G/s=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			EZv7RiSHvwpmlR7njvBPnudpOMo=
			</data>
			<key>hash2</key>
			<data>
			Q2V2n7w6SuJSEeberDLsPXIrUGE0Uw1r2c4r1BH7Z+0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			GilHhi8EvDKWBoYfKTS9FjHULGc=
			</data>
			<key>hash2</key>
			<data>
			NdIadHpRQ3ZLaGQDqhbL0p6gY0igo/5jYU9GOkL4G/s=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			wGs68ha3ug7gVOLDWonPi7KKQPE=
			</data>
			<key>hash2</key>
			<data>
			o2oYZoiZxEexNlKrahvZV3L1jPb4HJfzf9cN5r5dBL4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			y+gUA1FtOujWKEVS4YTcleVls9o=
			</data>
			<key>hash2</key>
			<data>
			KY863Ql4YNtF6GesYcGTu4BiAlZW2mh5YTYX9us2ccE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			80GlmyaWfvRsHfBbfau5nrhDt44=
			</data>
			<key>hash2</key>
			<data>
			v4yHUkOEVt2e9uKS7hX0plRz8chQSgdhT805DsIcfq0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Modules/FPAppsCapability.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			y+gUA1FtOujWKEVS4YTcleVls9o=
			</data>
			<key>hash2</key>
			<data>
			KY863Ql4YNtF6GesYcGTu4BiAlZW2mh5YTYX9us2ccE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			SUw+ieiL/PtLaQWkHpTQ7oq0694=
			</data>
			<key>hash2</key>
			<data>
			Q1FkQKuLDIjgaT/09klRKq2hdQ9FO/OCxfJjaePrt1o=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			lKKXNpnNbKjWqty7Ovu52zbl6ro=
			</data>
			<key>hash2</key>
			<data>
			hdywyE9Hf8tHcs5JqnqHYzJdqK5tXIiDzlLii7f3Bns=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPAppsCapability.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			tcCKjjcaxJVgNQqZCc4ex15ACqw=
			</data>
			<key>hash2</key>
			<data>
			jfTjECIcTOyRP7bmHoz2kk+rjIzrPckmUp9Nwao8zFc=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
