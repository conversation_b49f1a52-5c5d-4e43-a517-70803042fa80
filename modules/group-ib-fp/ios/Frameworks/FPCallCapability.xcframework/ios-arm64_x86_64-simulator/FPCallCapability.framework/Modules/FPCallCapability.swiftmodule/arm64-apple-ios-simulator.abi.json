{"ABIRoot": {"kind": "Root", "name": "FPCallCapability", "printedName": "FPCallCapability", "children": [{"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "FPCallCapability"}, {"kind": "Import", "name": "GIBMobileSdk", "printedName": "GIBMobileSdk", "declKind": "Import", "moduleName": "FPCallCapability"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "FPCallCapability"}, {"kind": "Import", "name": "GIBMobileSdk", "printedName": "GIBMobileSdk", "declKind": "Import", "moduleName": "FPCallCapability"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "FPCallCapability"}, {"kind": "Import", "name": "CallKit", "printedName": "CallKit", "declKind": "Import", "moduleName": "FPCallCapability"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "FPCallCapability"}, {"kind": "Import", "name": "CallKit", "printedName": "CallKit", "declKind": "Import", "moduleName": "FPCallCapability"}, {"kind": "Import", "name": "GIBMobileSdk", "printedName": "GIBMobileSdk", "declKind": "Import", "moduleName": "FPCallCapability"}], "json_format_version": 8}, "ConstValues": [{"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/FPCallCapability/GIBPacketStorageProtocol.swift", "kind": "Dictionary", "offset": 581, "length": 3, "value": "[]"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/FPCallCapability/CallCapability.swift", "kind": "Array", "offset": 584, "length": 2, "value": "[]"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/FPCallCapability/CallCapability.swift", "kind": "StringLiteral", "offset": 6264, "length": 6, "value": "\"call\""}]}