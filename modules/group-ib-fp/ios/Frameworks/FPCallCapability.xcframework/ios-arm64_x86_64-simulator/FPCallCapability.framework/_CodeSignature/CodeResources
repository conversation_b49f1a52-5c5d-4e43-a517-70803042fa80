<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/FPCallCapability.h</key>
		<data>
		GQ2TNnEY//VzDxQPk1GHRehgZ9w=
		</data>
		<key>Info.plist</key>
		<data>
		uqB83x3GitMYF8XhbeV+Kjm2NuM=
		</data>
		<key>Modules/FPCallCapability.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		l9pJ5F8KABRaNvjWVKVpSnGxK60=
		</data>
		<key>Modules/FPCallCapability.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		MPYygRk19COcq4y0QUAV0xV3ihg=
		</data>
		<key>Modules/FPCallCapability.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		rcfj36lMlvw7JbtcwpjReuKOtzM=
		</data>
		<key>Modules/FPCallCapability.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		ucfHpkraRtlmJ/AHS16UY5Hbr2U=
		</data>
		<key>Modules/FPCallCapability.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		L8RDcPeRmOBP0xyu/EOSQvX/YZ4=
		</data>
		<key>Modules/FPCallCapability.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<data>
		ucfHpkraRtlmJ/AHS16UY5Hbr2U=
		</data>
		<key>Modules/FPCallCapability.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		vgrSSS99SDb5zNy++/fjsOli+nQ=
		</data>
		<key>Modules/FPCallCapability.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		rcfj36lMlvw7JbtcwpjReuKOtzM=
		</data>
		<key>Modules/FPCallCapability.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		tdDPAoVGOufjmOIas1AEvfFjSRs=
		</data>
		<key>Modules/FPCallCapability.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		bGD1xo87B2zzGv7D2wQCrOAsbgw=
		</data>
		<key>Modules/FPCallCapability.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		tdDPAoVGOufjmOIas1AEvfFjSRs=
		</data>
		<key>Modules/FPCallCapability.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		yNdLX/vPSFg3wruOuHPWO1OfR0s=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		3uft3avcvA7nMeW91cZIW2JIoWs=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		Kk6MIwCTFQILcjJ07iPTg+nmyDc=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/FPCallCapability.h</key>
		<dict>
			<key>hash2</key>
			<data>
			CqCgAPN6QsVcNeAb5Z8kTCP0olmkDcxEUAMEA/1IfgU=
			</data>
		</dict>
		<key>Modules/FPCallCapability.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			lu+o8jfZ+Xo9wrv0igPHBl8GUYUPQr0Va0XzOhh36GY=
			</data>
		</dict>
		<key>Modules/FPCallCapability.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			FbLx7LqnYbZgGbvqQTHOb0l1bugR5L8uv91tFJVPUWU=
			</data>
		</dict>
		<key>Modules/FPCallCapability.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UUKDTVGG0EY5qnzlirSa4LRDqQg4/OL5qAL+sUUTMsI=
			</data>
		</dict>
		<key>Modules/FPCallCapability.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			n8WBX1cjQmJXndLGJE8FfgYmt/vn5Vq87LVMtPfgAaM=
			</data>
		</dict>
		<key>Modules/FPCallCapability.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			CNnCCSRskVqi/tYR0udtsuB73UQvZ+FxGQ1YK2gG7Ds=
			</data>
		</dict>
		<key>Modules/FPCallCapability.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			n8WBX1cjQmJXndLGJE8FfgYmt/vn5Vq87LVMtPfgAaM=
			</data>
		</dict>
		<key>Modules/FPCallCapability.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			znvV+W4clk/CVJQnkNngLB5lezhTGdgofcd3ftKv1JQ=
			</data>
		</dict>
		<key>Modules/FPCallCapability.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UUKDTVGG0EY5qnzlirSa4LRDqQg4/OL5qAL+sUUTMsI=
			</data>
		</dict>
		<key>Modules/FPCallCapability.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			wtX0KcqAKiXu7pCNsrC68/HGdLGUJ6U8pMrGSrFLT2g=
			</data>
		</dict>
		<key>Modules/FPCallCapability.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			rhdETxBblrUEUOJ1IoBzd5+rNng+IwCBTA6AJhWpCZw=
			</data>
		</dict>
		<key>Modules/FPCallCapability.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			wtX0KcqAKiXu7pCNsrC68/HGdLGUJ6U8pMrGSrFLT2g=
			</data>
		</dict>
		<key>Modules/FPCallCapability.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			SH7o9wEGaKnK5StwXDUfBVJi4mC9KNAQGGZ5MCQeb7Y=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			yRG+6tVoTeLL9igzer17LS31VEjdmjvE5+gg+ikr2h0=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			l+MvRzKE33A6X8khB2lW74SnZcqee6LnumE1BlLRJyc=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
