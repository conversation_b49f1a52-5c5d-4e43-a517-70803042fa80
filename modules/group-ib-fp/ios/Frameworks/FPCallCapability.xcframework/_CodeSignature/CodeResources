<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ios-arm64/FPCallCapability.framework/FPCallCapability</key>
		<data>
		tbCHrPJijJWUmfcNmoiWHg6fqOs=
		</data>
		<key>ios-arm64/FPCallCapability.framework/Headers/FPCallCapability.h</key>
		<data>
		GQ2TNnEY//VzDxQPk1GHRehgZ9w=
		</data>
		<key>ios-arm64/FPCallCapability.framework/Info.plist</key>
		<data>
		fgQHVdq2zRakD2ABHAXYSHvPeDg=
		</data>
		<key>ios-arm64/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo</key>
		<data>
		qj7DjMQ11YQgWHZo7ExanB6OjcE=
		</data>
		<key>ios-arm64/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/arm64-apple-ios.abi.json</key>
		<data>
		rcfj36lMlvw7JbtcwpjReuKOtzM=
		</data>
		<key>ios-arm64/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<data>
		i1WPr8rY0yHk2aEpxKtD316aCis=
		</data>
		<key>ios-arm64/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<data>
		Z0F6qicdgn1vP0XKb/NdbZZV6bk=
		</data>
		<key>ios-arm64/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<data>
		i1WPr8rY0yHk2aEpxKtD316aCis=
		</data>
		<key>ios-arm64/FPCallCapability.framework/Modules/module.modulemap</key>
		<data>
		3uft3avcvA7nMeW91cZIW2JIoWs=
		</data>
		<key>ios-arm64/FPCallCapability.framework/PrivacyInfo.xcprivacy</key>
		<data>
		Kk6MIwCTFQILcjJ07iPTg+nmyDc=
		</data>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/FPCallCapability</key>
		<data>
		43ORGu3bjVO1mmHRbAoRiLHHIrQ=
		</data>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Headers/FPCallCapability.h</key>
		<data>
		GQ2TNnEY//VzDxQPk1GHRehgZ9w=
		</data>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Info.plist</key>
		<data>
		uqB83x3GitMYF8XhbeV+Kjm2NuM=
		</data>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		l9pJ5F8KABRaNvjWVKVpSnGxK60=
		</data>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		MPYygRk19COcq4y0QUAV0xV3ihg=
		</data>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		rcfj36lMlvw7JbtcwpjReuKOtzM=
		</data>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		ucfHpkraRtlmJ/AHS16UY5Hbr2U=
		</data>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		L8RDcPeRmOBP0xyu/EOSQvX/YZ4=
		</data>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<data>
		ucfHpkraRtlmJ/AHS16UY5Hbr2U=
		</data>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		rcfj36lMlvw7JbtcwpjReuKOtzM=
		</data>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		tdDPAoVGOufjmOIas1AEvfFjSRs=
		</data>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		bGD1xo87B2zzGv7D2wQCrOAsbgw=
		</data>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		tdDPAoVGOufjmOIas1AEvfFjSRs=
		</data>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Modules/module.modulemap</key>
		<data>
		3uft3avcvA7nMeW91cZIW2JIoWs=
		</data>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/PrivacyInfo.xcprivacy</key>
		<data>
		Kk6MIwCTFQILcjJ07iPTg+nmyDc=
		</data>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/_CodeSignature/CodeResources</key>
		<data>
		0RZ/pde6hI2LpIzb8u4leVJOmws=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>ios-arm64/FPCallCapability.framework/FPCallCapability</key>
		<dict>
			<key>hash</key>
			<data>
			tbCHrPJijJWUmfcNmoiWHg6fqOs=
			</data>
			<key>hash2</key>
			<data>
			frdC+3wKqlfKBdi6rc1+h7OxMslVZDbT/bwEdJlYsFc=
			</data>
		</dict>
		<key>ios-arm64/FPCallCapability.framework/Headers/FPCallCapability.h</key>
		<dict>
			<key>hash</key>
			<data>
			GQ2TNnEY//VzDxQPk1GHRehgZ9w=
			</data>
			<key>hash2</key>
			<data>
			CqCgAPN6QsVcNeAb5Z8kTCP0olmkDcxEUAMEA/1IfgU=
			</data>
		</dict>
		<key>ios-arm64/FPCallCapability.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			fgQHVdq2zRakD2ABHAXYSHvPeDg=
			</data>
			<key>hash2</key>
			<data>
			p0fbUUk8sWUba4rwadGXiuMMzPmjXOsdt4FA3z56RQE=
			</data>
		</dict>
		<key>ios-arm64/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			qj7DjMQ11YQgWHZo7ExanB6OjcE=
			</data>
			<key>hash2</key>
			<data>
			rTsoXtjE/waPemrCtgb7BuuRfqIz3GwpmArGndGMDrY=
			</data>
		</dict>
		<key>ios-arm64/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/arm64-apple-ios.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			rcfj36lMlvw7JbtcwpjReuKOtzM=
			</data>
			<key>hash2</key>
			<data>
			UUKDTVGG0EY5qnzlirSa4LRDqQg4/OL5qAL+sUUTMsI=
			</data>
		</dict>
		<key>ios-arm64/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			i1WPr8rY0yHk2aEpxKtD316aCis=
			</data>
			<key>hash2</key>
			<data>
			KW++IpC0jdC6U0Y9lfHhEA2AyMfblCWiXdWHRwqsKOo=
			</data>
		</dict>
		<key>ios-arm64/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			Z0F6qicdgn1vP0XKb/NdbZZV6bk=
			</data>
			<key>hash2</key>
			<data>
			bBUURcSG3h3Zgk0HJ7XtjU4GqfqkZuJ8fP/LpYWcIfA=
			</data>
		</dict>
		<key>ios-arm64/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			i1WPr8rY0yHk2aEpxKtD316aCis=
			</data>
			<key>hash2</key>
			<data>
			KW++IpC0jdC6U0Y9lfHhEA2AyMfblCWiXdWHRwqsKOo=
			</data>
		</dict>
		<key>ios-arm64/FPCallCapability.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			3uft3avcvA7nMeW91cZIW2JIoWs=
			</data>
			<key>hash2</key>
			<data>
			yRG+6tVoTeLL9igzer17LS31VEjdmjvE5+gg+ikr2h0=
			</data>
		</dict>
		<key>ios-arm64/FPCallCapability.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			Kk6MIwCTFQILcjJ07iPTg+nmyDc=
			</data>
			<key>hash2</key>
			<data>
			l+MvRzKE33A6X8khB2lW74SnZcqee6LnumE1BlLRJyc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/FPCallCapability</key>
		<dict>
			<key>hash</key>
			<data>
			43ORGu3bjVO1mmHRbAoRiLHHIrQ=
			</data>
			<key>hash2</key>
			<data>
			hoSd5FOhYTSQ5oqvGXtZ51oan2e3NV/lWkYWTeeFlTo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Headers/FPCallCapability.h</key>
		<dict>
			<key>hash</key>
			<data>
			GQ2TNnEY//VzDxQPk1GHRehgZ9w=
			</data>
			<key>hash2</key>
			<data>
			CqCgAPN6QsVcNeAb5Z8kTCP0olmkDcxEUAMEA/1IfgU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			uqB83x3GitMYF8XhbeV+Kjm2NuM=
			</data>
			<key>hash2</key>
			<data>
			Vi/8XItL4vZGGHjVziknZAeZ4/WXgAMHEBYyFFD0F/U=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			l9pJ5F8KABRaNvjWVKVpSnGxK60=
			</data>
			<key>hash2</key>
			<data>
			lu+o8jfZ+Xo9wrv0igPHBl8GUYUPQr0Va0XzOhh36GY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			MPYygRk19COcq4y0QUAV0xV3ihg=
			</data>
			<key>hash2</key>
			<data>
			FbLx7LqnYbZgGbvqQTHOb0l1bugR5L8uv91tFJVPUWU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			rcfj36lMlvw7JbtcwpjReuKOtzM=
			</data>
			<key>hash2</key>
			<data>
			UUKDTVGG0EY5qnzlirSa4LRDqQg4/OL5qAL+sUUTMsI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			ucfHpkraRtlmJ/AHS16UY5Hbr2U=
			</data>
			<key>hash2</key>
			<data>
			n8WBX1cjQmJXndLGJE8FfgYmt/vn5Vq87LVMtPfgAaM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			L8RDcPeRmOBP0xyu/EOSQvX/YZ4=
			</data>
			<key>hash2</key>
			<data>
			CNnCCSRskVqi/tYR0udtsuB73UQvZ+FxGQ1YK2gG7Ds=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			ucfHpkraRtlmJ/AHS16UY5Hbr2U=
			</data>
			<key>hash2</key>
			<data>
			n8WBX1cjQmJXndLGJE8FfgYmt/vn5Vq87LVMtPfgAaM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			rcfj36lMlvw7JbtcwpjReuKOtzM=
			</data>
			<key>hash2</key>
			<data>
			UUKDTVGG0EY5qnzlirSa4LRDqQg4/OL5qAL+sUUTMsI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			tdDPAoVGOufjmOIas1AEvfFjSRs=
			</data>
			<key>hash2</key>
			<data>
			wtX0KcqAKiXu7pCNsrC68/HGdLGUJ6U8pMrGSrFLT2g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			bGD1xo87B2zzGv7D2wQCrOAsbgw=
			</data>
			<key>hash2</key>
			<data>
			rhdETxBblrUEUOJ1IoBzd5+rNng+IwCBTA6AJhWpCZw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Modules/FPCallCapability.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			tdDPAoVGOufjmOIas1AEvfFjSRs=
			</data>
			<key>hash2</key>
			<data>
			wtX0KcqAKiXu7pCNsrC68/HGdLGUJ6U8pMrGSrFLT2g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			3uft3avcvA7nMeW91cZIW2JIoWs=
			</data>
			<key>hash2</key>
			<data>
			yRG+6tVoTeLL9igzer17LS31VEjdmjvE5+gg+ikr2h0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			Kk6MIwCTFQILcjJ07iPTg+nmyDc=
			</data>
			<key>hash2</key>
			<data>
			l+MvRzKE33A6X8khB2lW74SnZcqee6LnumE1BlLRJyc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FPCallCapability.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			0RZ/pde6hI2LpIzb8u4leVJOmws=
			</data>
			<key>hash2</key>
			<data>
			B432p8GwAcm3euTWwbGbHCmP7VaPyPg7R+GL5xD0brI=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
