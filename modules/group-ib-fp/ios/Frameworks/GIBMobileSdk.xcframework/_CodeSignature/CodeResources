<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ios-arm64/GIBMobileSdk.framework/GIBMobileSdk</key>
		<data>
		OsKw9v2c5c4YmycOfpSyXOB9w9U=
		</data>
		<key>ios-arm64/GIBMobileSdk.framework/Headers/GIBAttribute.h</key>
		<data>
		2SG4NuJVmoUlQhqipY1u8plpuho=
		</data>
		<key>ios-arm64/GIBMobileSdk.framework/Headers/GIBAttributeFormat.h</key>
		<data>
		b2GEN0T6t5RKVwhFtO21v5Dtq74=
		</data>
		<key>ios-arm64/GIBMobileSdk.framework/Headers/GIBAttributeTitleKey.h</key>
		<data>
		NvpCe5X7trcO3qMp/INRdV47GMA=
		</data>
		<key>ios-arm64/GIBMobileSdk.framework/Headers/GIBLogsHandler.h</key>
		<data>
		j+E9p/ZbAhbhPv8+KvtXZEsIU1o=
		</data>
		<key>ios-arm64/GIBMobileSdk.framework/Headers/GIBMobileSDK-Swift.h</key>
		<data>
		zwT0Fr7wcOj7Btj7SCvSWs1G3PE=
		</data>
		<key>ios-arm64/GIBMobileSdk.framework/Headers/GIBMobileSDK.h</key>
		<data>
		9hub7RLYoVb+NImklyGZDJPwhtU=
		</data>
		<key>ios-arm64/GIBMobileSdk.framework/Headers/GIBNetworkListener.h</key>
		<data>
		Ct82sHP+YuCru8AL4t5GPmQfZ6o=
		</data>
		<key>ios-arm64/GIBMobileSdk.framework/Headers/GIBProxy.h</key>
		<data>
		SCqYtkKqNKHoP+Ys/owreKZYUrI=
		</data>
		<key>ios-arm64/GIBMobileSdk.framework/Headers/GIBSessionListener.h</key>
		<data>
		aC4tdK+WvBm9aikWyhmpsSbx+5E=
		</data>
		<key>ios-arm64/GIBMobileSdk.framework/Headers/MobileSDKError.h</key>
		<data>
		97qjy7FRw3C/b3HbG/Oob9rVzvw=
		</data>
		<key>ios-arm64/GIBMobileSdk.framework/Info.plist</key>
		<data>
		wHab7CNKQpU6xXM36zR+ndWgF48=
		</data>
		<key>ios-arm64/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo</key>
		<data>
		iP6N10DfINaEsZtxnkXwbbTppUk=
		</data>
		<key>ios-arm64/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios.abi.json</key>
		<data>
		9eN+UiM2TN+rw/1vLtBNfq/LAdk=
		</data>
		<key>ios-arm64/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<data>
		AqKBWJtYOszP0H8XfCmIdbwS03g=
		</data>
		<key>ios-arm64/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<data>
		E3pQhK8AwA5in1OjUDbKp9COlSc=
		</data>
		<key>ios-arm64/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<data>
		AqKBWJtYOszP0H8XfCmIdbwS03g=
		</data>
		<key>ios-arm64/GIBMobileSdk.framework/Modules/module.modulemap</key>
		<data>
		0AT1Yl8kCQ1phEEyW1MmvKu7aHg=
		</data>
		<key>ios-arm64/GIBMobileSdk.framework/PrivacyInfo.xcprivacy</key>
		<data>
		hSRIaPPkSTjqPuqnJ5VIqnoUal0=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/GIBMobileSdk</key>
		<data>
		+hwAH7FEjAbBf3VVNL6oAI9cbX4=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Headers/GIBAttribute.h</key>
		<data>
		2SG4NuJVmoUlQhqipY1u8plpuho=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Headers/GIBAttributeFormat.h</key>
		<data>
		b2GEN0T6t5RKVwhFtO21v5Dtq74=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Headers/GIBAttributeTitleKey.h</key>
		<data>
		NvpCe5X7trcO3qMp/INRdV47GMA=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Headers/GIBLogsHandler.h</key>
		<data>
		j+E9p/ZbAhbhPv8+KvtXZEsIU1o=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Headers/GIBMobileSDK-Swift.h</key>
		<data>
		Njqwf8v75XCy4Khr2X82Fw091pE=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Headers/GIBMobileSDK.h</key>
		<data>
		9hub7RLYoVb+NImklyGZDJPwhtU=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Headers/GIBNetworkListener.h</key>
		<data>
		Ct82sHP+YuCru8AL4t5GPmQfZ6o=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Headers/GIBProxy.h</key>
		<data>
		SCqYtkKqNKHoP+Ys/owreKZYUrI=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Headers/GIBSessionListener.h</key>
		<data>
		aC4tdK+WvBm9aikWyhmpsSbx+5E=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Headers/MobileSDKError.h</key>
		<data>
		97qjy7FRw3C/b3HbG/Oob9rVzvw=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Info.plist</key>
		<data>
		njdMyu6KoNMHpVp9wY8DJDnBfWo=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		bMHHCw8jcGGCHXrim7rqs9FZ2GI=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		nXmar67KRDWkTIYSXcLh9u260Zw=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		PWn6+gvn1ZQvoEy7L7Q52+jmxDQ=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		EJP4ZOdCNmDjbv7RhsUsx1xVjwM=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		aRvY6/IPnKB4E+sd42qRAmhmorM=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<data>
		EJP4ZOdCNmDjbv7RhsUsx1xVjwM=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		kCEoHY4h8V8+58HH4FA1ghZy86o=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		rrFk51nfJV9EUIAdA5q5RCCEnnM=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		tomjwV+/+7IxlMdESC69U/SDsGU=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		rrFk51nfJV9EUIAdA5q5RCCEnnM=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Modules/module.modulemap</key>
		<data>
		0AT1Yl8kCQ1phEEyW1MmvKu7aHg=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/PrivacyInfo.xcprivacy</key>
		<data>
		hSRIaPPkSTjqPuqnJ5VIqnoUal0=
		</data>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/_CodeSignature/CodeResources</key>
		<data>
		7woZal5DpK5z8QujuQmJY5rn8fE=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>ios-arm64/GIBMobileSdk.framework/GIBMobileSdk</key>
		<dict>
			<key>hash</key>
			<data>
			OsKw9v2c5c4YmycOfpSyXOB9w9U=
			</data>
			<key>hash2</key>
			<data>
			dYI0KwxYCIE9WVL3M+IaiVDQRIDyzUQLMvKDfGEM2nY=
			</data>
		</dict>
		<key>ios-arm64/GIBMobileSdk.framework/Headers/GIBAttribute.h</key>
		<dict>
			<key>hash</key>
			<data>
			2SG4NuJVmoUlQhqipY1u8plpuho=
			</data>
			<key>hash2</key>
			<data>
			/0ZuYs3Vat9wgcVWP1VhekFYVw0wR5JLI+uQnO65QuU=
			</data>
		</dict>
		<key>ios-arm64/GIBMobileSdk.framework/Headers/GIBAttributeFormat.h</key>
		<dict>
			<key>hash</key>
			<data>
			b2GEN0T6t5RKVwhFtO21v5Dtq74=
			</data>
			<key>hash2</key>
			<data>
			rV0wAHvGIu8kquI7Kwu7IYz/FED+IjrSGr5JbjPzVEM=
			</data>
		</dict>
		<key>ios-arm64/GIBMobileSdk.framework/Headers/GIBAttributeTitleKey.h</key>
		<dict>
			<key>hash</key>
			<data>
			NvpCe5X7trcO3qMp/INRdV47GMA=
			</data>
			<key>hash2</key>
			<data>
			pSEuiPXAVSkvQ+D/W6RA6yDWeHQNAMFV66v4RmM4m9k=
			</data>
		</dict>
		<key>ios-arm64/GIBMobileSdk.framework/Headers/GIBLogsHandler.h</key>
		<dict>
			<key>hash</key>
			<data>
			j+E9p/ZbAhbhPv8+KvtXZEsIU1o=
			</data>
			<key>hash2</key>
			<data>
			1S1NZaCwazMYJR2euIgdpyDHkdYeiKYibtELBjANaCU=
			</data>
		</dict>
		<key>ios-arm64/GIBMobileSdk.framework/Headers/GIBMobileSDK-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			zwT0Fr7wcOj7Btj7SCvSWs1G3PE=
			</data>
			<key>hash2</key>
			<data>
			soIk8zu8nssuNQRICMNav2JTtPKsDFEGLLsMtI0XRf0=
			</data>
		</dict>
		<key>ios-arm64/GIBMobileSdk.framework/Headers/GIBMobileSDK.h</key>
		<dict>
			<key>hash</key>
			<data>
			9hub7RLYoVb+NImklyGZDJPwhtU=
			</data>
			<key>hash2</key>
			<data>
			t5T5bGGeF5qPMcuBmBiHB+rFz9fxcVLpZXes+qgSWSU=
			</data>
		</dict>
		<key>ios-arm64/GIBMobileSdk.framework/Headers/GIBNetworkListener.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ct82sHP+YuCru8AL4t5GPmQfZ6o=
			</data>
			<key>hash2</key>
			<data>
			zds2pCGJ/PElUGfdAxc5dlwFBSB0gjcZ0jtn3WrfgfI=
			</data>
		</dict>
		<key>ios-arm64/GIBMobileSdk.framework/Headers/GIBProxy.h</key>
		<dict>
			<key>hash</key>
			<data>
			SCqYtkKqNKHoP+Ys/owreKZYUrI=
			</data>
			<key>hash2</key>
			<data>
			4gumi3iD+fAUGWgCl/idCAkreGd5jIWFqCaEhpnwtMg=
			</data>
		</dict>
		<key>ios-arm64/GIBMobileSdk.framework/Headers/GIBSessionListener.h</key>
		<dict>
			<key>hash</key>
			<data>
			aC4tdK+WvBm9aikWyhmpsSbx+5E=
			</data>
			<key>hash2</key>
			<data>
			HHLfeQfLUnB3UhSwmGT4mmKcQ8U5yoNbEv00lR82ClI=
			</data>
		</dict>
		<key>ios-arm64/GIBMobileSdk.framework/Headers/MobileSDKError.h</key>
		<dict>
			<key>hash</key>
			<data>
			97qjy7FRw3C/b3HbG/Oob9rVzvw=
			</data>
			<key>hash2</key>
			<data>
			irybSaHMtp/klB4EYj2MXxx4dt7xphnKPx0ngBvq4Z0=
			</data>
		</dict>
		<key>ios-arm64/GIBMobileSdk.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			wHab7CNKQpU6xXM36zR+ndWgF48=
			</data>
			<key>hash2</key>
			<data>
			OTAdScqPZJjiSkCwQDi5Q3T3EiPD4VgwdCfW6CE84AY=
			</data>
		</dict>
		<key>ios-arm64/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			iP6N10DfINaEsZtxnkXwbbTppUk=
			</data>
			<key>hash2</key>
			<data>
			F6brFvQiPYZAxXGxvqkzPnnc+uFvUqSkxTWS0DXo86s=
			</data>
		</dict>
		<key>ios-arm64/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			9eN+UiM2TN+rw/1vLtBNfq/LAdk=
			</data>
			<key>hash2</key>
			<data>
			qZQp134IrAzFBGulrpW3Gi6QH7yR56OSwAblzRpEIoc=
			</data>
		</dict>
		<key>ios-arm64/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			AqKBWJtYOszP0H8XfCmIdbwS03g=
			</data>
			<key>hash2</key>
			<data>
			SvIUoxBZ8FYPRdcZ5k2qh0m3tGA+gVQ4QQQ2bwWgmTA=
			</data>
		</dict>
		<key>ios-arm64/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			E3pQhK8AwA5in1OjUDbKp9COlSc=
			</data>
			<key>hash2</key>
			<data>
			zR8lggbCWBdpQ/NaFrBrdaq+5OmpGuD/pARWl8wsId8=
			</data>
		</dict>
		<key>ios-arm64/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			AqKBWJtYOszP0H8XfCmIdbwS03g=
			</data>
			<key>hash2</key>
			<data>
			SvIUoxBZ8FYPRdcZ5k2qh0m3tGA+gVQ4QQQ2bwWgmTA=
			</data>
		</dict>
		<key>ios-arm64/GIBMobileSdk.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			0AT1Yl8kCQ1phEEyW1MmvKu7aHg=
			</data>
			<key>hash2</key>
			<data>
			ccXeVxfLyk2b/AFaVZ+HWNU7Gx1tGyV6OtTpcZ+Gwr4=
			</data>
		</dict>
		<key>ios-arm64/GIBMobileSdk.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			hSRIaPPkSTjqPuqnJ5VIqnoUal0=
			</data>
			<key>hash2</key>
			<data>
			ul+ub7gpkCaC5cKFNf5yKmtRfrwpK6xhCCZ7YUCmg3U=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/GIBMobileSdk</key>
		<dict>
			<key>hash</key>
			<data>
			+hwAH7FEjAbBf3VVNL6oAI9cbX4=
			</data>
			<key>hash2</key>
			<data>
			m1T/J3ecTT2EEaTtOCIGSqTqrozkV5aZCDrGixyFoM4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Headers/GIBAttribute.h</key>
		<dict>
			<key>hash</key>
			<data>
			2SG4NuJVmoUlQhqipY1u8plpuho=
			</data>
			<key>hash2</key>
			<data>
			/0ZuYs3Vat9wgcVWP1VhekFYVw0wR5JLI+uQnO65QuU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Headers/GIBAttributeFormat.h</key>
		<dict>
			<key>hash</key>
			<data>
			b2GEN0T6t5RKVwhFtO21v5Dtq74=
			</data>
			<key>hash2</key>
			<data>
			rV0wAHvGIu8kquI7Kwu7IYz/FED+IjrSGr5JbjPzVEM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Headers/GIBAttributeTitleKey.h</key>
		<dict>
			<key>hash</key>
			<data>
			NvpCe5X7trcO3qMp/INRdV47GMA=
			</data>
			<key>hash2</key>
			<data>
			pSEuiPXAVSkvQ+D/W6RA6yDWeHQNAMFV66v4RmM4m9k=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Headers/GIBLogsHandler.h</key>
		<dict>
			<key>hash</key>
			<data>
			j+E9p/ZbAhbhPv8+KvtXZEsIU1o=
			</data>
			<key>hash2</key>
			<data>
			1S1NZaCwazMYJR2euIgdpyDHkdYeiKYibtELBjANaCU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Headers/GIBMobileSDK-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			Njqwf8v75XCy4Khr2X82Fw091pE=
			</data>
			<key>hash2</key>
			<data>
			vJaN4wheLoUfa5eIyClnooPUdNkbgLCsYklkyx+I5sU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Headers/GIBMobileSDK.h</key>
		<dict>
			<key>hash</key>
			<data>
			9hub7RLYoVb+NImklyGZDJPwhtU=
			</data>
			<key>hash2</key>
			<data>
			t5T5bGGeF5qPMcuBmBiHB+rFz9fxcVLpZXes+qgSWSU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Headers/GIBNetworkListener.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ct82sHP+YuCru8AL4t5GPmQfZ6o=
			</data>
			<key>hash2</key>
			<data>
			zds2pCGJ/PElUGfdAxc5dlwFBSB0gjcZ0jtn3WrfgfI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Headers/GIBProxy.h</key>
		<dict>
			<key>hash</key>
			<data>
			SCqYtkKqNKHoP+Ys/owreKZYUrI=
			</data>
			<key>hash2</key>
			<data>
			4gumi3iD+fAUGWgCl/idCAkreGd5jIWFqCaEhpnwtMg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Headers/GIBSessionListener.h</key>
		<dict>
			<key>hash</key>
			<data>
			aC4tdK+WvBm9aikWyhmpsSbx+5E=
			</data>
			<key>hash2</key>
			<data>
			HHLfeQfLUnB3UhSwmGT4mmKcQ8U5yoNbEv00lR82ClI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Headers/MobileSDKError.h</key>
		<dict>
			<key>hash</key>
			<data>
			97qjy7FRw3C/b3HbG/Oob9rVzvw=
			</data>
			<key>hash2</key>
			<data>
			irybSaHMtp/klB4EYj2MXxx4dt7xphnKPx0ngBvq4Z0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			njdMyu6KoNMHpVp9wY8DJDnBfWo=
			</data>
			<key>hash2</key>
			<data>
			Rf+cwXO2E0ABMsazh5Am5m8CC7KGaqHJ8rEh+nfuERo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			bMHHCw8jcGGCHXrim7rqs9FZ2GI=
			</data>
			<key>hash2</key>
			<data>
			gYDoxmQ8J203rL9xf+Z2in5sIkygPuoT6ozzjaz/0l0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			nXmar67KRDWkTIYSXcLh9u260Zw=
			</data>
			<key>hash2</key>
			<data>
			orVo6Xj7H4Yebq8LK1f6lk2lYBZ/4mbhnXpxvl59jkA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			PWn6+gvn1ZQvoEy7L7Q52+jmxDQ=
			</data>
			<key>hash2</key>
			<data>
			M6HA2c4Dde1XJiZadD3pTULn5bdWP3lx8J09XIE3Pm8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			EJP4ZOdCNmDjbv7RhsUsx1xVjwM=
			</data>
			<key>hash2</key>
			<data>
			s7XyPOEniiSK3ue+Z7sf9pCBccaJHD93o6XRu53HGDY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			aRvY6/IPnKB4E+sd42qRAmhmorM=
			</data>
			<key>hash2</key>
			<data>
			kAS+nqw8L1S2DR0t9Z1dra6SviGfSKqgn4uQsxpeXu8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			EJP4ZOdCNmDjbv7RhsUsx1xVjwM=
			</data>
			<key>hash2</key>
			<data>
			s7XyPOEniiSK3ue+Z7sf9pCBccaJHD93o6XRu53HGDY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			kCEoHY4h8V8+58HH4FA1ghZy86o=
			</data>
			<key>hash2</key>
			<data>
			1dkQTVTFeuoocsPmqi6o/doyYH8D0pBDgxNf8AUvwwo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			rrFk51nfJV9EUIAdA5q5RCCEnnM=
			</data>
			<key>hash2</key>
			<data>
			aW/hgmoLRUoHateuDN+Eh8iySlR6kceB/MR63zJbln4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			tomjwV+/+7IxlMdESC69U/SDsGU=
			</data>
			<key>hash2</key>
			<data>
			+ZWZX7yISc7q7YfCb2cia8kiGKmS00zS0T+jG0lIOLU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Modules/GIBMobileSdk.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			rrFk51nfJV9EUIAdA5q5RCCEnnM=
			</data>
			<key>hash2</key>
			<data>
			aW/hgmoLRUoHateuDN+Eh8iySlR6kceB/MR63zJbln4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			0AT1Yl8kCQ1phEEyW1MmvKu7aHg=
			</data>
			<key>hash2</key>
			<data>
			ccXeVxfLyk2b/AFaVZ+HWNU7Gx1tGyV6OtTpcZ+Gwr4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			hSRIaPPkSTjqPuqnJ5VIqnoUal0=
			</data>
			<key>hash2</key>
			<data>
			ul+ub7gpkCaC5cKFNf5yKmtRfrwpK6xhCCZ7YUCmg3U=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GIBMobileSdk.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			7woZal5DpK5z8QujuQmJY5rn8fE=
			</data>
			<key>hash2</key>
			<data>
			lcZjgm5KlLpVSswoZ0egXzg43KB41lthTqzhGJYGxcU=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
