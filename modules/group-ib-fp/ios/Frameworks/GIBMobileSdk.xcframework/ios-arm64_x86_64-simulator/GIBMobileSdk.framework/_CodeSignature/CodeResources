<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/GIBAttribute.h</key>
		<data>
		2SG4NuJVmoUlQhqipY1u8plpuho=
		</data>
		<key>Headers/GIBAttributeFormat.h</key>
		<data>
		b2GEN0T6t5RKVwhFtO21v5Dtq74=
		</data>
		<key>Headers/GIBAttributeTitleKey.h</key>
		<data>
		NvpCe5X7trcO3qMp/INRdV47GMA=
		</data>
		<key>Headers/GIBLogsHandler.h</key>
		<data>
		j+E9p/ZbAhbhPv8+KvtXZEsIU1o=
		</data>
		<key>Headers/GIBMobileSDK-Swift.h</key>
		<data>
		Njqwf8v75XCy4Khr2X82Fw091pE=
		</data>
		<key>Headers/GIBMobileSDK.h</key>
		<data>
		9hub7RLYoVb+NImklyGZDJPwhtU=
		</data>
		<key>Headers/GIBNetworkListener.h</key>
		<data>
		Ct82sHP+YuCru8AL4t5GPmQfZ6o=
		</data>
		<key>Headers/GIBProxy.h</key>
		<data>
		SCqYtkKqNKHoP+Ys/owreKZYUrI=
		</data>
		<key>Headers/GIBSessionListener.h</key>
		<data>
		aC4tdK+WvBm9aikWyhmpsSbx+5E=
		</data>
		<key>Headers/MobileSDKError.h</key>
		<data>
		97qjy7FRw3C/b3HbG/Oob9rVzvw=
		</data>
		<key>Info.plist</key>
		<data>
		njdMyu6KoNMHpVp9wY8DJDnBfWo=
		</data>
		<key>Modules/GIBMobileSdk.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		bMHHCw8jcGGCHXrim7rqs9FZ2GI=
		</data>
		<key>Modules/GIBMobileSdk.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		nXmar67KRDWkTIYSXcLh9u260Zw=
		</data>
		<key>Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		PWn6+gvn1ZQvoEy7L7Q52+jmxDQ=
		</data>
		<key>Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		EJP4ZOdCNmDjbv7RhsUsx1xVjwM=
		</data>
		<key>Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		aRvY6/IPnKB4E+sd42qRAmhmorM=
		</data>
		<key>Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<data>
		EJP4ZOdCNmDjbv7RhsUsx1xVjwM=
		</data>
		<key>Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		wXKmBSt+6B0o8Tf0ysmCyixlWb8=
		</data>
		<key>Modules/GIBMobileSdk.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		kCEoHY4h8V8+58HH4FA1ghZy86o=
		</data>
		<key>Modules/GIBMobileSdk.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		rrFk51nfJV9EUIAdA5q5RCCEnnM=
		</data>
		<key>Modules/GIBMobileSdk.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		tomjwV+/+7IxlMdESC69U/SDsGU=
		</data>
		<key>Modules/GIBMobileSdk.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		rrFk51nfJV9EUIAdA5q5RCCEnnM=
		</data>
		<key>Modules/GIBMobileSdk.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		ZIcvk3bSCUWMdyRHgWEXwlgbT7A=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		0AT1Yl8kCQ1phEEyW1MmvKu7aHg=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		hSRIaPPkSTjqPuqnJ5VIqnoUal0=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/GIBAttribute.h</key>
		<dict>
			<key>hash2</key>
			<data>
			/0ZuYs3Vat9wgcVWP1VhekFYVw0wR5JLI+uQnO65QuU=
			</data>
		</dict>
		<key>Headers/GIBAttributeFormat.h</key>
		<dict>
			<key>hash2</key>
			<data>
			rV0wAHvGIu8kquI7Kwu7IYz/FED+IjrSGr5JbjPzVEM=
			</data>
		</dict>
		<key>Headers/GIBAttributeTitleKey.h</key>
		<dict>
			<key>hash2</key>
			<data>
			pSEuiPXAVSkvQ+D/W6RA6yDWeHQNAMFV66v4RmM4m9k=
			</data>
		</dict>
		<key>Headers/GIBLogsHandler.h</key>
		<dict>
			<key>hash2</key>
			<data>
			1S1NZaCwazMYJR2euIgdpyDHkdYeiKYibtELBjANaCU=
			</data>
		</dict>
		<key>Headers/GIBMobileSDK-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			vJaN4wheLoUfa5eIyClnooPUdNkbgLCsYklkyx+I5sU=
			</data>
		</dict>
		<key>Headers/GIBMobileSDK.h</key>
		<dict>
			<key>hash2</key>
			<data>
			t5T5bGGeF5qPMcuBmBiHB+rFz9fxcVLpZXes+qgSWSU=
			</data>
		</dict>
		<key>Headers/GIBNetworkListener.h</key>
		<dict>
			<key>hash2</key>
			<data>
			zds2pCGJ/PElUGfdAxc5dlwFBSB0gjcZ0jtn3WrfgfI=
			</data>
		</dict>
		<key>Headers/GIBProxy.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4gumi3iD+fAUGWgCl/idCAkreGd5jIWFqCaEhpnwtMg=
			</data>
		</dict>
		<key>Headers/GIBSessionListener.h</key>
		<dict>
			<key>hash2</key>
			<data>
			HHLfeQfLUnB3UhSwmGT4mmKcQ8U5yoNbEv00lR82ClI=
			</data>
		</dict>
		<key>Headers/MobileSDKError.h</key>
		<dict>
			<key>hash2</key>
			<data>
			irybSaHMtp/klB4EYj2MXxx4dt7xphnKPx0ngBvq4Z0=
			</data>
		</dict>
		<key>Modules/GIBMobileSdk.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			gYDoxmQ8J203rL9xf+Z2in5sIkygPuoT6ozzjaz/0l0=
			</data>
		</dict>
		<key>Modules/GIBMobileSdk.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			orVo6Xj7H4Yebq8LK1f6lk2lYBZ/4mbhnXpxvl59jkA=
			</data>
		</dict>
		<key>Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			M6HA2c4Dde1XJiZadD3pTULn5bdWP3lx8J09XIE3Pm8=
			</data>
		</dict>
		<key>Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			s7XyPOEniiSK3ue+Z7sf9pCBccaJHD93o6XRu53HGDY=
			</data>
		</dict>
		<key>Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			kAS+nqw8L1S2DR0t9Z1dra6SviGfSKqgn4uQsxpeXu8=
			</data>
		</dict>
		<key>Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			s7XyPOEniiSK3ue+Z7sf9pCBccaJHD93o6XRu53HGDY=
			</data>
		</dict>
		<key>Modules/GIBMobileSdk.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			73+mSlEfJbXJvIvsdJ+BNpj4h8qr/UbulLLzXG86p40=
			</data>
		</dict>
		<key>Modules/GIBMobileSdk.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1dkQTVTFeuoocsPmqi6o/doyYH8D0pBDgxNf8AUvwwo=
			</data>
		</dict>
		<key>Modules/GIBMobileSdk.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			aW/hgmoLRUoHateuDN+Eh8iySlR6kceB/MR63zJbln4=
			</data>
		</dict>
		<key>Modules/GIBMobileSdk.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			+ZWZX7yISc7q7YfCb2cia8kiGKmS00zS0T+jG0lIOLU=
			</data>
		</dict>
		<key>Modules/GIBMobileSdk.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			aW/hgmoLRUoHateuDN+Eh8iySlR6kceB/MR63zJbln4=
			</data>
		</dict>
		<key>Modules/GIBMobileSdk.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			j+rWyjzZ0XCm1ybwb/j9+kv8mmHPmT9vefZnPo7ziPg=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			ccXeVxfLyk2b/AFaVZ+HWNU7Gx1tGyV6OtTpcZ+Gwr4=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			ul+ub7gpkCaC5cKFNf5yKmtRfrwpK6xhCCZ7YUCmg3U=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
