#if 0
#elif defined(__arm64__) && __arm64__
// Generated by Apple Swift version 6.0.3 effective-5.10 (swiftlang-6.0.3.1.10 clang-1600.0.30.1)
#ifndef GIBMOBILESDK_SWIFT_H
#define GIBMOBILESDK_SWIFT_H
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wgcc-compat"

#if !defined(__has_include)
# define __has_include(x) 0
#endif
#if !defined(__has_attribute)
# define __has_attribute(x) 0
#endif
#if !defined(__has_feature)
# define __has_feature(x) 0
#endif
#if !defined(__has_warning)
# define __has_warning(x) 0
#endif

#if __has_include(<swift/objc-prologue.h>)
# include <swift/objc-prologue.h>
#endif

#pragma clang diagnostic ignored "-Wauto-import"
#if defined(__OBJC__)
#include <Foundation/Foundation.h>
#endif
#if defined(__cplusplus)
#include <cstdint>
#include <cstddef>
#include <cstdbool>
#include <cstring>
#include <stdlib.h>
#include <new>
#include <type_traits>
#else
#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include <string.h>
#endif
#if defined(__cplusplus)
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wnon-modular-include-in-framework-module"
#if defined(__arm64e__) && __has_include(<ptrauth.h>)
# include <ptrauth.h>
#else
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wreserved-macro-identifier"
# ifndef __ptrauth_swift_value_witness_function_pointer
#  define __ptrauth_swift_value_witness_function_pointer(x)
# endif
# ifndef __ptrauth_swift_class_method_pointer
#  define __ptrauth_swift_class_method_pointer(x)
# endif
#pragma clang diagnostic pop
#endif
#pragma clang diagnostic pop
#endif

#if !defined(SWIFT_TYPEDEFS)
# define SWIFT_TYPEDEFS 1
# if __has_include(<uchar.h>)
#  include <uchar.h>
# elif !defined(__cplusplus)
typedef uint_least16_t char16_t;
typedef uint_least32_t char32_t;
# endif
typedef float swift_float2  __attribute__((__ext_vector_type__(2)));
typedef float swift_float3  __attribute__((__ext_vector_type__(3)));
typedef float swift_float4  __attribute__((__ext_vector_type__(4)));
typedef double swift_double2  __attribute__((__ext_vector_type__(2)));
typedef double swift_double3  __attribute__((__ext_vector_type__(3)));
typedef double swift_double4  __attribute__((__ext_vector_type__(4)));
typedef int swift_int2  __attribute__((__ext_vector_type__(2)));
typedef int swift_int3  __attribute__((__ext_vector_type__(3)));
typedef int swift_int4  __attribute__((__ext_vector_type__(4)));
typedef unsigned int swift_uint2  __attribute__((__ext_vector_type__(2)));
typedef unsigned int swift_uint3  __attribute__((__ext_vector_type__(3)));
typedef unsigned int swift_uint4  __attribute__((__ext_vector_type__(4)));
#endif

#if !defined(SWIFT_PASTE)
# define SWIFT_PASTE_HELPER(x, y) x##y
# define SWIFT_PASTE(x, y) SWIFT_PASTE_HELPER(x, y)
#endif
#if !defined(SWIFT_METATYPE)
# define SWIFT_METATYPE(X) Class
#endif
#if !defined(SWIFT_CLASS_PROPERTY)
# if __has_feature(objc_class_property)
#  define SWIFT_CLASS_PROPERTY(...) __VA_ARGS__
# else
#  define SWIFT_CLASS_PROPERTY(...) 
# endif
#endif
#if !defined(SWIFT_RUNTIME_NAME)
# if __has_attribute(objc_runtime_name)
#  define SWIFT_RUNTIME_NAME(X) __attribute__((objc_runtime_name(X)))
# else
#  define SWIFT_RUNTIME_NAME(X) 
# endif
#endif
#if !defined(SWIFT_COMPILE_NAME)
# if __has_attribute(swift_name)
#  define SWIFT_COMPILE_NAME(X) __attribute__((swift_name(X)))
# else
#  define SWIFT_COMPILE_NAME(X) 
# endif
#endif
#if !defined(SWIFT_METHOD_FAMILY)
# if __has_attribute(objc_method_family)
#  define SWIFT_METHOD_FAMILY(X) __attribute__((objc_method_family(X)))
# else
#  define SWIFT_METHOD_FAMILY(X) 
# endif
#endif
#if !defined(SWIFT_NOESCAPE)
# if __has_attribute(noescape)
#  define SWIFT_NOESCAPE __attribute__((noescape))
# else
#  define SWIFT_NOESCAPE 
# endif
#endif
#if !defined(SWIFT_RELEASES_ARGUMENT)
# if __has_attribute(ns_consumed)
#  define SWIFT_RELEASES_ARGUMENT __attribute__((ns_consumed))
# else
#  define SWIFT_RELEASES_ARGUMENT 
# endif
#endif
#if !defined(SWIFT_WARN_UNUSED_RESULT)
# if __has_attribute(warn_unused_result)
#  define SWIFT_WARN_UNUSED_RESULT __attribute__((warn_unused_result))
# else
#  define SWIFT_WARN_UNUSED_RESULT 
# endif
#endif
#if !defined(SWIFT_NORETURN)
# if __has_attribute(noreturn)
#  define SWIFT_NORETURN __attribute__((noreturn))
# else
#  define SWIFT_NORETURN 
# endif
#endif
#if !defined(SWIFT_CLASS_EXTRA)
# define SWIFT_CLASS_EXTRA 
#endif
#if !defined(SWIFT_PROTOCOL_EXTRA)
# define SWIFT_PROTOCOL_EXTRA 
#endif
#if !defined(SWIFT_ENUM_EXTRA)
# define SWIFT_ENUM_EXTRA 
#endif
#if !defined(SWIFT_CLASS)
# if __has_attribute(objc_subclassing_restricted)
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# else
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# endif
#endif
#if !defined(SWIFT_RESILIENT_CLASS)
# if __has_attribute(objc_class_stub)
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME) __attribute__((objc_class_stub))
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_class_stub)) SWIFT_CLASS_NAMED(SWIFT_NAME)
# else
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME)
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) SWIFT_CLASS_NAMED(SWIFT_NAME)
# endif
#endif
#if !defined(SWIFT_PROTOCOL)
# define SWIFT_PROTOCOL(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
# define SWIFT_PROTOCOL_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
#endif
#if !defined(SWIFT_EXTENSION)
# define SWIFT_EXTENSION(M) SWIFT_PASTE(M##_Swift_, __LINE__)
#endif
#if !defined(OBJC_DESIGNATED_INITIALIZER)
# if __has_attribute(objc_designated_initializer)
#  define OBJC_DESIGNATED_INITIALIZER __attribute__((objc_designated_initializer))
# else
#  define OBJC_DESIGNATED_INITIALIZER 
# endif
#endif
#if !defined(SWIFT_ENUM_ATTR)
# if __has_attribute(enum_extensibility)
#  define SWIFT_ENUM_ATTR(_extensibility) __attribute__((enum_extensibility(_extensibility)))
# else
#  define SWIFT_ENUM_ATTR(_extensibility) 
# endif
#endif
#if !defined(SWIFT_ENUM)
# define SWIFT_ENUM(_type, _name, _extensibility) enum _name : _type _name; enum SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# if __has_feature(generalized_swift_name)
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) enum _name : _type _name SWIFT_COMPILE_NAME(SWIFT_NAME); enum SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# else
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) SWIFT_ENUM(_type, _name, _extensibility)
# endif
#endif
#if !defined(SWIFT_UNAVAILABLE)
# define SWIFT_UNAVAILABLE __attribute__((unavailable))
#endif
#if !defined(SWIFT_UNAVAILABLE_MSG)
# define SWIFT_UNAVAILABLE_MSG(msg) __attribute__((unavailable(msg)))
#endif
#if !defined(SWIFT_AVAILABILITY)
# define SWIFT_AVAILABILITY(plat, ...) __attribute__((availability(plat, __VA_ARGS__)))
#endif
#if !defined(SWIFT_WEAK_IMPORT)
# define SWIFT_WEAK_IMPORT __attribute__((weak_import))
#endif
#if !defined(SWIFT_DEPRECATED)
# define SWIFT_DEPRECATED __attribute__((deprecated))
#endif
#if !defined(SWIFT_DEPRECATED_MSG)
# define SWIFT_DEPRECATED_MSG(...) __attribute__((deprecated(__VA_ARGS__)))
#endif
#if !defined(SWIFT_DEPRECATED_OBJC)
# if __has_feature(attribute_diagnose_if_objc)
#  define SWIFT_DEPRECATED_OBJC(Msg) __attribute__((diagnose_if(1, Msg, "warning")))
# else
#  define SWIFT_DEPRECATED_OBJC(Msg) SWIFT_DEPRECATED_MSG(Msg)
# endif
#endif
#if defined(__OBJC__)
#if !defined(IBSegueAction)
# define IBSegueAction 
#endif
#endif
#if !defined(SWIFT_EXTERN)
# if defined(__cplusplus)
#  define SWIFT_EXTERN extern "C"
# else
#  define SWIFT_EXTERN extern
# endif
#endif
#if !defined(SWIFT_CALL)
# define SWIFT_CALL __attribute__((swiftcall))
#endif
#if !defined(SWIFT_INDIRECT_RESULT)
# define SWIFT_INDIRECT_RESULT __attribute__((swift_indirect_result))
#endif
#if !defined(SWIFT_CONTEXT)
# define SWIFT_CONTEXT __attribute__((swift_context))
#endif
#if !defined(SWIFT_ERROR_RESULT)
# define SWIFT_ERROR_RESULT __attribute__((swift_error_result))
#endif
#if defined(__cplusplus)
# define SWIFT_NOEXCEPT noexcept
#else
# define SWIFT_NOEXCEPT 
#endif
#if !defined(SWIFT_C_INLINE_THUNK)
# if __has_attribute(always_inline)
# if __has_attribute(nodebug)
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline)) __attribute__((nodebug))
# else
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline))
# endif
# else
#  define SWIFT_C_INLINE_THUNK inline
# endif
#endif
#if defined(_WIN32)
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL __declspec(dllimport)
#endif
#else
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL 
#endif
#endif
#if defined(__OBJC__)
#if __has_feature(objc_modules)
#if __has_warning("-Watimport-in-framework-header")
#pragma clang diagnostic ignored "-Watimport-in-framework-header"
#endif
@import AppTrackingTransparency;
@import CoreFoundation;
@import CoreLocation;
@import Foundation;
@import ObjectiveC;
@import UIKit;
@import WebKit;
#endif

#endif
#pragma clang diagnostic ignored "-Wproperty-attribute-mismatch"
#pragma clang diagnostic ignored "-Wduplicate-method-arg"
#if __has_warning("-Wpragma-clang-attribute")
# pragma clang diagnostic ignored "-Wpragma-clang-attribute"
#endif
#pragma clang diagnostic ignored "-Wunknown-pragmas"
#pragma clang diagnostic ignored "-Wnullability"
#pragma clang diagnostic ignored "-Wdollar-in-identifier-extension"
#pragma clang diagnostic ignored "-Wunsafe-buffer-usage"

#if __has_attribute(external_source_symbol)
# pragma push_macro("any")
# undef any
# pragma clang attribute push(__attribute__((external_source_symbol(language="Swift", defined_in="GIBMobileSdk",generated_declaration))), apply_to=any(function,enum,objc_interface,objc_category,objc_protocol))
# pragma pop_macro("any")
#endif

#if defined(__OBJC__)

SWIFT_CLASS("_TtC12GIBMobileSdk15BehaviorManager")
@interface BehaviorManager : NSObject
SWIFT_CLASS_PROPERTY(@property (nonatomic, class) BOOL isExtendedData;)
+ (BOOL)isExtendedData SWIFT_WARN_UNUSED_RESULT;
+ (void)setIsExtendedData:(BOOL)value;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class NSCoder;

SWIFT_CLASS("_TtC12GIBMobileSdk12FPSecureView")
@interface FPSecureView : UIView
@property (nonatomic, readonly, strong) UIView * _Nullable secureContainer;
/// Default true
@property (nonatomic) BOOL isPreventScreenshot;
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)coder OBJC_DESIGNATED_INITIALIZER;
@end


SWIFT_CLASS("_TtC12GIBMobileSdk16GIBProtectedView")
@interface GIBProtectedView : UITextField
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)coder OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithFrame:(CGRect)frame SWIFT_UNAVAILABLE;
@end

enum Capability : NSInteger;
@class NSString;
@protocol GIBSwiftManagerDelegate;

/// GIBSwiftManager
SWIFT_CLASS("_TtC12GIBMobileSdk15GIBSwiftManager")
@interface GIBSwiftManager : NSObject
/// Instance of GIBSwiftManager
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) GIBSwiftManager * _Nonnull shared;)
+ (GIBSwiftManager * _Nonnull)shared SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
/// The enableCapability method initiates the SDK modules.
/// \param capability Module, which will be enabled
///
///
/// returns:
/// true - if successfully run; false - if already run
- (BOOL)enableCapability:(enum Capability)capability;
/// The disableCapability method disables the SDK module.
/// \param capability Module, which will be disabled
///
///
/// returns:
/// true - if successfully stop; false - if capability not run
- (BOOL)disableCapability:(enum Capability)capability;
/// Run all capabilities
- (void)run;
/// Stop all capabilities
- (void)stop;
/// Check if Capability run or not
/// \param capability Capability object
///
///
/// returns:
/// true or false
- (BOOL)isRun:(enum Capability)capability SWIFT_WARN_UNUSED_RESULT;
/// Get status for all capabilities
///
/// returns:
/// Dictionary of status on capability
- (NSDictionary<NSString *, NSNumber *> * _Nonnull)allStatus SWIFT_WARN_UNUSED_RESULT;
/// Don’t use this method.
/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// warning:
/// UNSAFE - DO NOT USE
- (void)setOutput:(id <GIBSwiftManagerDelegate> _Nonnull)delegate;
/// Get marketing name for <em>Capability</em> object
/// \param capability Capability object
///
///
/// returns:
/// Marketing name
- (NSString * _Nullable)getCapabilityName:(enum Capability)capability SWIFT_WARN_UNUSED_RESULT;
- (void)requestIDFAPermissionWithCompletion:(void (^ _Nonnull)(enum ATTrackingManagerAuthorizationStatus))completion SWIFT_AVAILABILITY(tvos,introduced=14) SWIFT_AVAILABILITY(ios,introduced=14);
- (enum ATTrackingManagerAuthorizationStatus)getIDFAPermission SWIFT_WARN_UNUSED_RESULT SWIFT_AVAILABILITY(tvos,introduced=14) SWIFT_AVAILABILITY(ios,introduced=14);
@end

/// Part modules of SDK.
/// Modules that are enabled by default can be disabled by calling the <a href="https://fhwiki.group-ib.tech/Integration/ios_sdk/#disableCapability%20method">disableCapability</a> method, and then re-enabled by calling the <a href="https://fhwiki.group-ib.tech/Integration/ios_sdk/#enableCapability%20method">enableCapability</a> method again.
typedef SWIFT_ENUM(NSInteger, Capability, open) {
/// BatteryStatusCapability
/// The module logs the device battery parameters: current charge level, status of charging/connection to a charger or network.
/// requires:
/// Supported on devices with iOS 9+.
  CapabilityBatteryStatus = 0,
/// CellularCapability
/// The module collects the parameters of the device’s SIM card: communication technology (2G, 3G, LTE, etc.), mobileCountryCode, mobileNetworkCode, carrierName, isoCountryCode, allowsVOIP.
/// requires:
/// Supported on devices with iOS 9+.
  CapabilityCellular = 1,
/// CallCapability
/// The module logs the events of calls at different stages: incoming or outgoing call, starting a call, ending a call, putting a call on hold.
/// All call events are registered, including calls from VoIP applications running on the CallKit framework.
/// important:
/// Only the event of the call itself is logged. Mobile SDK cannot access the caller’s number or call content.
/// attention:
/// In the China region, CallCapability will not work on devices with iOS 10+, because the use of the CallKit framework is prohibited in the PRC.
/// requires:
/// Supported on devices with iOS 10+.
  CapabilityCall = 2,
/// PasscodeCapability
/// The module checks if the user has set password and biometric data (Face ID/Touch ID) for siging into the application.
/// If the application can be accessed using biometrics and the system did not recognize the user, and the Mobile SDK is initialized for the first time right after that, the isDeviceBiometriceSet variable will be set to false.
/// important:
/// Mobile SDK does not access the contents of password or biometric container.
/// requires:
/// Supported on devices with iOS 9+.
  CapabilityPasscode = 3,
/// WebViewCapability
/// The module tracks all WKWebView objects in the application, monitors opening web pages and calls methods of the JavaScript module on them. If the WKWebView object was subscribed to a <a href="https://fhwiki.group-ib.tech/Integration/ios_sdk/#GIBWebViewNavigationDelegate%20сlass">navigationDelegate</a>, the WebViewCapability module proxies the delegate while maintaining the specified functionality.
/// note:
/// When connecting the WebViewCapability module, using the <a href="https://fhwiki.group-ib.tech/Integration/ios_sdk/#initAppWebView%20method">initAppWebView</a> and <a href="https://fhwiki.group-ib.tech/Integration/ios_sdk/#GIBWebViewNavigationDelegate%20сlass">GIBWebViewNavigationDelegate</a> is not required.
/// important:
/// For the WebViewCapability module to work, you must enable the SwizzleCapability module.
/// requires:
/// Supported on devices with iOS 9+.
  CapabilityWebView = 4,
/// NetworkCapability
/// The module receives the ssid and bssid parameters of the WiFi network to which the device is connected.
/// attention:
/// The module works if in the device settings the user has given the application permission to access geolocation data, the protected application is an active VPN application, or the protected application has configured a WiFi network that the device uses through NEHotspotConfiguration. The Mobile SDK does not ask for this permission on its own.
/// important:
/// For the module to work, in Xcode on the Signing & Capabilities tab, enable the <em>Access WiFi information</em> option.
/// requires:
/// Supported on devices with iOS 9+.
  CapabilityNetwork = 5,
/// MotionCapability
/// The module logs data from the device’s accelerometer, gravity sensor and gyroscope (X, Y, Z coordinates).
/// important:
/// For the MotionCapability module to work, you must enable the BehaviorCapability module.
/// requires:
/// Supported on devices with iOS/tvOS 9+.
  CapabilityMotion = 6,
/// SwizzleCapability
/// <h2>The module performs run-time injection into methods:</h2>
/// warning:
/// <em>Module should be enabled before calling the run method. After enabling, you can disable this module only by completely restarting the application.</em>
/// requires:
/// Supported on devices with iOS/tvOS 9+.
/// <ul>
///   <li>
///     `UIViewController: viewDidAppear(_ animated: Bool)
///   </li>
///   <li>
///     <code>UIApplication: sendEvent(_ event: UIEvent)</code>
///   </li>
/// </ul>
  CapabilitySwizzle = 7,
/// LocationCapability
/// The module collects data about the device’s location: latitude, longitude, acc, alt, course, alt_acc, speed_acc (for devices with iOS 10.0+), speed.
/// important:
/// Mobile SDK does not receive geolocation data when the application is not in the foreground.
/// warning:
/// The module works if in the device settings the user has given the application permission to access geolocation data. Mobile SDK does not ask for this permission on its own.
/// requires:
/// Supported on devices with iOS/tvOS 9+.
  CapabilityLocation = 8,
/// AudioCapability
/// The module monitors the audio output source that is currently being used.
/// important:
/// Only the fact of audio playback and its source is logged. Mobile SDK does not access audio content.
/// requires:
/// Supported on devices with iOS/tvOS 9+.
  CapabilityAudio = 9,
/// CloudIdentifierCapability
/// The module transmits and stores in iCloud Key-value storage a unique value for the cloudGibId parameter. Used to identify devices via <a href="http://fhwiki.group-ib.tech/Start/cloud_id/">Cloud ID</a>
/// The module will transfer service data for identifying user device with Cloud ID only after the user logs into the Apple account.
/// important:
/// For the module to work, in Xcode on the <em>Signing & Capabilities</em> tab, enable the <em>iCloud Key-value storage</em> option.
/// requires:
/// Supported on devices with iOS/tvOS 9+.
  CapabilityCloudIdentifier = 10,
/// DeviceStatusCapability
/// The module receives and processes information about device being close to the ear (data from the Proximity sensor).
/// note:
/// Allows to receive more data if used with the <em>CallCapability</em> module.
/// requires:
/// Supported on devices with iOS/tvOS 9+.
  CapabilityDeviceStatus = 11,
/// CaptureCapability
/// The module registers saving a screenshot (for devices with <em>iOS/tvOS 9+</em>) and recording a video of the screen (for devices with <em>iOS 11+</em>).
/// important:
/// Only the fact of saving a screenshot or recording the screen is logged. Mobile SDK cannot access the content of files.
/// requires:
/// Supported on devices with iOS 9+, tvOS 14+.
  CapabilityCapture = 12,
/// AppsCapability
/// The module receives and processes information about applications installed on the user’s device. Applications on the device are checked by calling the <em>UIApplication canOpenURL</em> method.
/// important:
/// For the module to work, add application keys to the Info.plist file of the protected application. To receive an up-to-date list of application keys, contact <a href="mailto:<EMAIL>">Group-IB specialists</a>.
/// requires:
/// Supported on devices with iOS/tvOS 9+.
  CapabilityApps = 13,
/// ProxyCapability
/// The module receives and processes information about the use of VPN or Proxy on the user’s device.
/// <h2>The module gets access to the parameters:</h2>
/// requires:
/// Supported on devices with iOS/tvOS 9+.
/// <ul>
///   <li>
///     http proxy port,
///   </li>
///   <li>
///     http proxy address,
///   </li>
///   <li>
///     http proxy user,
///   </li>
///   <li>
///     VPN protocol.
///   </li>
/// </ul>
  CapabilityProxy = 14,
/// KeyboardCapability
/// The module receives and processes information about keyboards on a mobile device (identifiers of installed software keyboards).
/// requires:
/// Supported on devices with iOS/tvOS 9+.
  CapabilityKeyboard = 15,
/// BehaviorCapability
/// The module is used to collect data about user behavior in the application: screen clicks, swipes, opening new UIViewController objects, UIView elements on the screen and their location, typing speed in UITextField and UITextView.
/// note:
/// <em>Typing speed in a UITextField with isSecure token is not logged.</em>
/// important:
/// For the BehaviorCapability module to work, you must enable the SwizzleCapability module.
/// requires:
/// Supported on devices with iOS/tvOS 9+.
  CapabilityBehavior = 16,
/// PreventScreenshotsCapability
/// This module can auto prevent take screenshots by user. Work for screenshots, screensharing, remote control, Xcode screenshots.
  CapabilityPreventScreenshots = 17,
/// SecurityCapability
/// This module can detect jailbreak on device
  CapabilitySecurity = 18,
/// AdvertiseCapability
/// The module gets the IDFA number from the user’s device if the user has given permission.
/// important:
/// The module don’t request permission from user.
  CapabilityAdvertise = 19,
  CapabilityPortScan = 20,
  CapabilityGlobalId = 21,
};



enum LogType : NSInteger;
@class WKWebView;

/// Internal.
/// warning:
/// DO NOT USE.
SWIFT_PROTOCOL("_TtP12GIBMobileSdk23GIBSwiftManagerDelegate_")
@protocol GIBSwiftManagerDelegate
/// Internal.
/// warning:
/// DO NOT USE.
- (void)sendDictionaryFromSwiftWithDictionary:(NSDictionary * _Nonnull)dictionary isRepeatData:(BOOL)isRepeatData;
/// Internal.
/// warning:
/// DO NOT USE.
- (void)sendLog:(NSString * _Nonnull)log type:(enum LogType)type;
/// Internal.
/// warning:
/// DO NOT USE.
- (void)sendInitAppWebView:(WKWebView * _Nonnull)webView;
@end

@class WKNavigation;
@class WKNavigationAction;
@class WKWebpagePreferences;
@class WKNavigationResponse;
@class NSURLAuthenticationChallenge;
@class NSURLCredential;
@class WKDownload;

/// The <em>GIBWebViewNavigationDelegate</em> class implements methods of the WKNavigationDelegate protocol to track new web pages opened within the application and invoke JavaScript module methods on them.
SWIFT_CLASS("_TtC12GIBMobileSdk28GIBWebViewNavigationDelegate")
@interface GIBWebViewNavigationDelegate : NSObject <WKNavigationDelegate>
/// Standard initialization
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
/// Initialization with the possibility to transmit custom implementation of the WKNavigationDelegate protocol.
/// In this case, the GIBWebViewNavigationDelegate will proxy the transmitted methods into its implementation.
/// \param navigationDelegate Object of the WKNavigationDelegate protocol implementation
///
- (nonnull instancetype)initWithNavigationDelegate:(id <WKNavigationDelegate> _Nullable)navigationDelegate OBJC_DESIGNATED_INITIALIZER;
- (void)webView:(WKWebView * _Nonnull)webView didFinishNavigation:(WKNavigation * _Null_unspecified)navigation;
- (void)webView:(WKWebView * _Nonnull)webView decidePolicyForNavigationAction:(WKNavigationAction * _Nonnull)navigationAction decisionHandler:(void (^ _Nonnull)(WKNavigationActionPolicy))decisionHandler;
- (void)webView:(WKWebView * _Nonnull)webView decidePolicyForNavigationAction:(WKNavigationAction * _Nonnull)navigationAction preferences:(WKWebpagePreferences * _Nonnull)preferences decisionHandler:(void (^ _Nonnull)(WKNavigationActionPolicy, WKWebpagePreferences * _Nonnull))decisionHandler SWIFT_AVAILABILITY(ios,introduced=13.0);
- (void)webView:(WKWebView * _Nonnull)webView decidePolicyForNavigationResponse:(WKNavigationResponse * _Nonnull)navigationResponse decisionHandler:(void (^ _Nonnull)(WKNavigationResponsePolicy))decisionHandler;
- (void)webView:(WKWebView * _Nonnull)webView didStartProvisionalNavigation:(WKNavigation * _Null_unspecified)navigation;
- (void)webView:(WKWebView * _Nonnull)webView didReceiveServerRedirectForProvisionalNavigation:(WKNavigation * _Null_unspecified)navigation;
- (void)webView:(WKWebView * _Nonnull)webView didFailProvisionalNavigation:(WKNavigation * _Null_unspecified)navigation withError:(NSError * _Nonnull)error;
- (void)webView:(WKWebView * _Nonnull)webView didCommitNavigation:(WKNavigation * _Null_unspecified)navigation;
- (void)webView:(WKWebView * _Nonnull)webView didFailNavigation:(WKNavigation * _Null_unspecified)navigation withError:(NSError * _Nonnull)error;
- (void)webView:(WKWebView * _Nonnull)webView didReceiveAuthenticationChallenge:(NSURLAuthenticationChallenge * _Nonnull)challenge completionHandler:(void (^ _Nonnull)(NSURLSessionAuthChallengeDisposition, NSURLCredential * _Nullable))completionHandler;
- (void)webViewWebContentProcessDidTerminate:(WKWebView * _Nonnull)webView;
- (void)webView:(WKWebView * _Nonnull)webView authenticationChallenge:(NSURLAuthenticationChallenge * _Nonnull)challenge shouldAllowDeprecatedTLS:(void (^ _Nonnull)(BOOL))decisionHandler SWIFT_AVAILABILITY(ios,introduced=14.0);
- (void)webView:(WKWebView * _Nonnull)webView navigationAction:(WKNavigationAction * _Nonnull)navigationAction didBecomeDownload:(WKDownload * _Nonnull)download SWIFT_AVAILABILITY(ios,introduced=14.5);
- (void)webView:(WKWebView * _Nonnull)webView navigationResponse:(WKNavigationResponse * _Nonnull)navigationResponse didBecomeDownload:(WKDownload * _Nonnull)download SWIFT_AVAILABILITY(ios,introduced=14.5);
@end


SWIFT_CLASS("_TtC12GIBMobileSdk15LocationService")
@interface LocationService : NSObject
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

@class CLLocationManager;
@class CLLocation;

@interface LocationService (SWIFT_EXTENSION(GIBMobileSdk)) <CLLocationManagerDelegate>
- (void)locationManager:(CLLocationManager * _Nonnull)manager didUpdateLocations:(NSArray<CLLocation *> * _Nonnull)locations;
- (void)locationManagerDidChangeAuthorization:(CLLocationManager * _Nonnull)manager;
- (void)locationManager:(CLLocationManager * _Nonnull)manager didFailWithError:(NSError * _Nonnull)error;
@end

/// Log types of SDK
typedef SWIFT_ENUM(NSInteger, LogType, open) {
/// Log-message with error
  LogTypeError = 0,
/// Log-message with warning
  LogTypeWarning = 1,
/// Info message
  LogTypeInfo = 2,
/// Verbosed log-message from Mobile SDK
  LogTypeVerbose = 3,
};





@interface UIView (SWIFT_EXTENSION(GIBMobileSdk))
- (void)fpPinLayout:(enum NSLayoutAttribute)type;
- (void)fpPinLayoutEdges;
@end




@interface UIWindow (SWIFT_EXTENSION(GIBMobileSdk))
/// Use this method for hide information on the UIWindow when user take screenshots, screensharing, remote control and Xcode screenshots
- (void)gibPreventScreenshots;
@end


@interface UIWindow (SWIFT_EXTENSION(GIBMobileSdk))
- (void)capabilityPreventScreenshots;
- (void)capabilityDisablePreventScreenshots;
@end


SWIFT_CLASS("_TtC12GIBMobileSdk13_FPSecureView")
@interface _FPSecureView : UITextField
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)coder OBJC_DESIGNATED_INITIALIZER;
@property (nonatomic, readonly, strong) UIView * _Nullable secureContainer;
@property (nonatomic, readonly) BOOL canBecomeFirstResponder;
- (BOOL)becomeFirstResponder SWIFT_WARN_UNUSED_RESULT;
@end

#endif
#if __has_attribute(external_source_symbol)
# pragma clang attribute pop
#endif
#if defined(__cplusplus)
#endif
#pragma clang diagnostic pop
#endif

#elif defined(__x86_64__) && __x86_64__
// Generated by Apple Swift version 6.0.3 effective-5.10 (swiftlang-6.0.3.1.10 clang-1600.0.30.1)
#ifndef GIBMOBILESDK_SWIFT_H
#define GIBMOBILESDK_SWIFT_H
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wgcc-compat"

#if !defined(__has_include)
# define __has_include(x) 0
#endif
#if !defined(__has_attribute)
# define __has_attribute(x) 0
#endif
#if !defined(__has_feature)
# define __has_feature(x) 0
#endif
#if !defined(__has_warning)
# define __has_warning(x) 0
#endif

#if __has_include(<swift/objc-prologue.h>)
# include <swift/objc-prologue.h>
#endif

#pragma clang diagnostic ignored "-Wauto-import"
#if defined(__OBJC__)
#include <Foundation/Foundation.h>
#endif
#if defined(__cplusplus)
#include <cstdint>
#include <cstddef>
#include <cstdbool>
#include <cstring>
#include <stdlib.h>
#include <new>
#include <type_traits>
#else
#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include <string.h>
#endif
#if defined(__cplusplus)
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wnon-modular-include-in-framework-module"
#if defined(__arm64e__) && __has_include(<ptrauth.h>)
# include <ptrauth.h>
#else
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wreserved-macro-identifier"
# ifndef __ptrauth_swift_value_witness_function_pointer
#  define __ptrauth_swift_value_witness_function_pointer(x)
# endif
# ifndef __ptrauth_swift_class_method_pointer
#  define __ptrauth_swift_class_method_pointer(x)
# endif
#pragma clang diagnostic pop
#endif
#pragma clang diagnostic pop
#endif

#if !defined(SWIFT_TYPEDEFS)
# define SWIFT_TYPEDEFS 1
# if __has_include(<uchar.h>)
#  include <uchar.h>
# elif !defined(__cplusplus)
typedef uint_least16_t char16_t;
typedef uint_least32_t char32_t;
# endif
typedef float swift_float2  __attribute__((__ext_vector_type__(2)));
typedef float swift_float3  __attribute__((__ext_vector_type__(3)));
typedef float swift_float4  __attribute__((__ext_vector_type__(4)));
typedef double swift_double2  __attribute__((__ext_vector_type__(2)));
typedef double swift_double3  __attribute__((__ext_vector_type__(3)));
typedef double swift_double4  __attribute__((__ext_vector_type__(4)));
typedef int swift_int2  __attribute__((__ext_vector_type__(2)));
typedef int swift_int3  __attribute__((__ext_vector_type__(3)));
typedef int swift_int4  __attribute__((__ext_vector_type__(4)));
typedef unsigned int swift_uint2  __attribute__((__ext_vector_type__(2)));
typedef unsigned int swift_uint3  __attribute__((__ext_vector_type__(3)));
typedef unsigned int swift_uint4  __attribute__((__ext_vector_type__(4)));
#endif

#if !defined(SWIFT_PASTE)
# define SWIFT_PASTE_HELPER(x, y) x##y
# define SWIFT_PASTE(x, y) SWIFT_PASTE_HELPER(x, y)
#endif
#if !defined(SWIFT_METATYPE)
# define SWIFT_METATYPE(X) Class
#endif
#if !defined(SWIFT_CLASS_PROPERTY)
# if __has_feature(objc_class_property)
#  define SWIFT_CLASS_PROPERTY(...) __VA_ARGS__
# else
#  define SWIFT_CLASS_PROPERTY(...) 
# endif
#endif
#if !defined(SWIFT_RUNTIME_NAME)
# if __has_attribute(objc_runtime_name)
#  define SWIFT_RUNTIME_NAME(X) __attribute__((objc_runtime_name(X)))
# else
#  define SWIFT_RUNTIME_NAME(X) 
# endif
#endif
#if !defined(SWIFT_COMPILE_NAME)
# if __has_attribute(swift_name)
#  define SWIFT_COMPILE_NAME(X) __attribute__((swift_name(X)))
# else
#  define SWIFT_COMPILE_NAME(X) 
# endif
#endif
#if !defined(SWIFT_METHOD_FAMILY)
# if __has_attribute(objc_method_family)
#  define SWIFT_METHOD_FAMILY(X) __attribute__((objc_method_family(X)))
# else
#  define SWIFT_METHOD_FAMILY(X) 
# endif
#endif
#if !defined(SWIFT_NOESCAPE)
# if __has_attribute(noescape)
#  define SWIFT_NOESCAPE __attribute__((noescape))
# else
#  define SWIFT_NOESCAPE 
# endif
#endif
#if !defined(SWIFT_RELEASES_ARGUMENT)
# if __has_attribute(ns_consumed)
#  define SWIFT_RELEASES_ARGUMENT __attribute__((ns_consumed))
# else
#  define SWIFT_RELEASES_ARGUMENT 
# endif
#endif
#if !defined(SWIFT_WARN_UNUSED_RESULT)
# if __has_attribute(warn_unused_result)
#  define SWIFT_WARN_UNUSED_RESULT __attribute__((warn_unused_result))
# else
#  define SWIFT_WARN_UNUSED_RESULT 
# endif
#endif
#if !defined(SWIFT_NORETURN)
# if __has_attribute(noreturn)
#  define SWIFT_NORETURN __attribute__((noreturn))
# else
#  define SWIFT_NORETURN 
# endif
#endif
#if !defined(SWIFT_CLASS_EXTRA)
# define SWIFT_CLASS_EXTRA 
#endif
#if !defined(SWIFT_PROTOCOL_EXTRA)
# define SWIFT_PROTOCOL_EXTRA 
#endif
#if !defined(SWIFT_ENUM_EXTRA)
# define SWIFT_ENUM_EXTRA 
#endif
#if !defined(SWIFT_CLASS)
# if __has_attribute(objc_subclassing_restricted)
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# else
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# endif
#endif
#if !defined(SWIFT_RESILIENT_CLASS)
# if __has_attribute(objc_class_stub)
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME) __attribute__((objc_class_stub))
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_class_stub)) SWIFT_CLASS_NAMED(SWIFT_NAME)
# else
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME)
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) SWIFT_CLASS_NAMED(SWIFT_NAME)
# endif
#endif
#if !defined(SWIFT_PROTOCOL)
# define SWIFT_PROTOCOL(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
# define SWIFT_PROTOCOL_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
#endif
#if !defined(SWIFT_EXTENSION)
# define SWIFT_EXTENSION(M) SWIFT_PASTE(M##_Swift_, __LINE__)
#endif
#if !defined(OBJC_DESIGNATED_INITIALIZER)
# if __has_attribute(objc_designated_initializer)
#  define OBJC_DESIGNATED_INITIALIZER __attribute__((objc_designated_initializer))
# else
#  define OBJC_DESIGNATED_INITIALIZER 
# endif
#endif
#if !defined(SWIFT_ENUM_ATTR)
# if __has_attribute(enum_extensibility)
#  define SWIFT_ENUM_ATTR(_extensibility) __attribute__((enum_extensibility(_extensibility)))
# else
#  define SWIFT_ENUM_ATTR(_extensibility) 
# endif
#endif
#if !defined(SWIFT_ENUM)
# define SWIFT_ENUM(_type, _name, _extensibility) enum _name : _type _name; enum SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# if __has_feature(generalized_swift_name)
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) enum _name : _type _name SWIFT_COMPILE_NAME(SWIFT_NAME); enum SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# else
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) SWIFT_ENUM(_type, _name, _extensibility)
# endif
#endif
#if !defined(SWIFT_UNAVAILABLE)
# define SWIFT_UNAVAILABLE __attribute__((unavailable))
#endif
#if !defined(SWIFT_UNAVAILABLE_MSG)
# define SWIFT_UNAVAILABLE_MSG(msg) __attribute__((unavailable(msg)))
#endif
#if !defined(SWIFT_AVAILABILITY)
# define SWIFT_AVAILABILITY(plat, ...) __attribute__((availability(plat, __VA_ARGS__)))
#endif
#if !defined(SWIFT_WEAK_IMPORT)
# define SWIFT_WEAK_IMPORT __attribute__((weak_import))
#endif
#if !defined(SWIFT_DEPRECATED)
# define SWIFT_DEPRECATED __attribute__((deprecated))
#endif
#if !defined(SWIFT_DEPRECATED_MSG)
# define SWIFT_DEPRECATED_MSG(...) __attribute__((deprecated(__VA_ARGS__)))
#endif
#if !defined(SWIFT_DEPRECATED_OBJC)
# if __has_feature(attribute_diagnose_if_objc)
#  define SWIFT_DEPRECATED_OBJC(Msg) __attribute__((diagnose_if(1, Msg, "warning")))
# else
#  define SWIFT_DEPRECATED_OBJC(Msg) SWIFT_DEPRECATED_MSG(Msg)
# endif
#endif
#if defined(__OBJC__)
#if !defined(IBSegueAction)
# define IBSegueAction 
#endif
#endif
#if !defined(SWIFT_EXTERN)
# if defined(__cplusplus)
#  define SWIFT_EXTERN extern "C"
# else
#  define SWIFT_EXTERN extern
# endif
#endif
#if !defined(SWIFT_CALL)
# define SWIFT_CALL __attribute__((swiftcall))
#endif
#if !defined(SWIFT_INDIRECT_RESULT)
# define SWIFT_INDIRECT_RESULT __attribute__((swift_indirect_result))
#endif
#if !defined(SWIFT_CONTEXT)
# define SWIFT_CONTEXT __attribute__((swift_context))
#endif
#if !defined(SWIFT_ERROR_RESULT)
# define SWIFT_ERROR_RESULT __attribute__((swift_error_result))
#endif
#if defined(__cplusplus)
# define SWIFT_NOEXCEPT noexcept
#else
# define SWIFT_NOEXCEPT 
#endif
#if !defined(SWIFT_C_INLINE_THUNK)
# if __has_attribute(always_inline)
# if __has_attribute(nodebug)
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline)) __attribute__((nodebug))
# else
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline))
# endif
# else
#  define SWIFT_C_INLINE_THUNK inline
# endif
#endif
#if defined(_WIN32)
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL __declspec(dllimport)
#endif
#else
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL 
#endif
#endif
#if defined(__OBJC__)
#if __has_feature(objc_modules)
#if __has_warning("-Watimport-in-framework-header")
#pragma clang diagnostic ignored "-Watimport-in-framework-header"
#endif
@import AppTrackingTransparency;
@import CoreFoundation;
@import CoreLocation;
@import Foundation;
@import ObjectiveC;
@import UIKit;
@import WebKit;
#endif

#endif
#pragma clang diagnostic ignored "-Wproperty-attribute-mismatch"
#pragma clang diagnostic ignored "-Wduplicate-method-arg"
#if __has_warning("-Wpragma-clang-attribute")
# pragma clang diagnostic ignored "-Wpragma-clang-attribute"
#endif
#pragma clang diagnostic ignored "-Wunknown-pragmas"
#pragma clang diagnostic ignored "-Wnullability"
#pragma clang diagnostic ignored "-Wdollar-in-identifier-extension"
#pragma clang diagnostic ignored "-Wunsafe-buffer-usage"

#if __has_attribute(external_source_symbol)
# pragma push_macro("any")
# undef any
# pragma clang attribute push(__attribute__((external_source_symbol(language="Swift", defined_in="GIBMobileSdk",generated_declaration))), apply_to=any(function,enum,objc_interface,objc_category,objc_protocol))
# pragma pop_macro("any")
#endif

#if defined(__OBJC__)

SWIFT_CLASS("_TtC12GIBMobileSdk15BehaviorManager")
@interface BehaviorManager : NSObject
SWIFT_CLASS_PROPERTY(@property (nonatomic, class) BOOL isExtendedData;)
+ (BOOL)isExtendedData SWIFT_WARN_UNUSED_RESULT;
+ (void)setIsExtendedData:(BOOL)value;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class NSCoder;

SWIFT_CLASS("_TtC12GIBMobileSdk12FPSecureView")
@interface FPSecureView : UIView
@property (nonatomic, readonly, strong) UIView * _Nullable secureContainer;
/// Default true
@property (nonatomic) BOOL isPreventScreenshot;
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)coder OBJC_DESIGNATED_INITIALIZER;
@end


SWIFT_CLASS("_TtC12GIBMobileSdk16GIBProtectedView")
@interface GIBProtectedView : UITextField
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)coder OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithFrame:(CGRect)frame SWIFT_UNAVAILABLE;
@end

enum Capability : NSInteger;
@class NSString;
@protocol GIBSwiftManagerDelegate;

/// GIBSwiftManager
SWIFT_CLASS("_TtC12GIBMobileSdk15GIBSwiftManager")
@interface GIBSwiftManager : NSObject
/// Instance of GIBSwiftManager
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) GIBSwiftManager * _Nonnull shared;)
+ (GIBSwiftManager * _Nonnull)shared SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
/// The enableCapability method initiates the SDK modules.
/// \param capability Module, which will be enabled
///
///
/// returns:
/// true - if successfully run; false - if already run
- (BOOL)enableCapability:(enum Capability)capability;
/// The disableCapability method disables the SDK module.
/// \param capability Module, which will be disabled
///
///
/// returns:
/// true - if successfully stop; false - if capability not run
- (BOOL)disableCapability:(enum Capability)capability;
/// Run all capabilities
- (void)run;
/// Stop all capabilities
- (void)stop;
/// Check if Capability run or not
/// \param capability Capability object
///
///
/// returns:
/// true or false
- (BOOL)isRun:(enum Capability)capability SWIFT_WARN_UNUSED_RESULT;
/// Get status for all capabilities
///
/// returns:
/// Dictionary of status on capability
- (NSDictionary<NSString *, NSNumber *> * _Nonnull)allStatus SWIFT_WARN_UNUSED_RESULT;
/// Don’t use this method.
/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// warning:
/// UNSAFE - DO NOT USE
- (void)setOutput:(id <GIBSwiftManagerDelegate> _Nonnull)delegate;
/// Get marketing name for <em>Capability</em> object
/// \param capability Capability object
///
///
/// returns:
/// Marketing name
- (NSString * _Nullable)getCapabilityName:(enum Capability)capability SWIFT_WARN_UNUSED_RESULT;
- (void)requestIDFAPermissionWithCompletion:(void (^ _Nonnull)(enum ATTrackingManagerAuthorizationStatus))completion SWIFT_AVAILABILITY(tvos,introduced=14) SWIFT_AVAILABILITY(ios,introduced=14);
- (enum ATTrackingManagerAuthorizationStatus)getIDFAPermission SWIFT_WARN_UNUSED_RESULT SWIFT_AVAILABILITY(tvos,introduced=14) SWIFT_AVAILABILITY(ios,introduced=14);
@end

/// Part modules of SDK.
/// Modules that are enabled by default can be disabled by calling the <a href="https://fhwiki.group-ib.tech/Integration/ios_sdk/#disableCapability%20method">disableCapability</a> method, and then re-enabled by calling the <a href="https://fhwiki.group-ib.tech/Integration/ios_sdk/#enableCapability%20method">enableCapability</a> method again.
typedef SWIFT_ENUM(NSInteger, Capability, open) {
/// BatteryStatusCapability
/// The module logs the device battery parameters: current charge level, status of charging/connection to a charger or network.
/// requires:
/// Supported on devices with iOS 9+.
  CapabilityBatteryStatus = 0,
/// CellularCapability
/// The module collects the parameters of the device’s SIM card: communication technology (2G, 3G, LTE, etc.), mobileCountryCode, mobileNetworkCode, carrierName, isoCountryCode, allowsVOIP.
/// requires:
/// Supported on devices with iOS 9+.
  CapabilityCellular = 1,
/// CallCapability
/// The module logs the events of calls at different stages: incoming or outgoing call, starting a call, ending a call, putting a call on hold.
/// All call events are registered, including calls from VoIP applications running on the CallKit framework.
/// important:
/// Only the event of the call itself is logged. Mobile SDK cannot access the caller’s number or call content.
/// attention:
/// In the China region, CallCapability will not work on devices with iOS 10+, because the use of the CallKit framework is prohibited in the PRC.
/// requires:
/// Supported on devices with iOS 10+.
  CapabilityCall = 2,
/// PasscodeCapability
/// The module checks if the user has set password and biometric data (Face ID/Touch ID) for siging into the application.
/// If the application can be accessed using biometrics and the system did not recognize the user, and the Mobile SDK is initialized for the first time right after that, the isDeviceBiometriceSet variable will be set to false.
/// important:
/// Mobile SDK does not access the contents of password or biometric container.
/// requires:
/// Supported on devices with iOS 9+.
  CapabilityPasscode = 3,
/// WebViewCapability
/// The module tracks all WKWebView objects in the application, monitors opening web pages and calls methods of the JavaScript module on them. If the WKWebView object was subscribed to a <a href="https://fhwiki.group-ib.tech/Integration/ios_sdk/#GIBWebViewNavigationDelegate%20сlass">navigationDelegate</a>, the WebViewCapability module proxies the delegate while maintaining the specified functionality.
/// note:
/// When connecting the WebViewCapability module, using the <a href="https://fhwiki.group-ib.tech/Integration/ios_sdk/#initAppWebView%20method">initAppWebView</a> and <a href="https://fhwiki.group-ib.tech/Integration/ios_sdk/#GIBWebViewNavigationDelegate%20сlass">GIBWebViewNavigationDelegate</a> is not required.
/// important:
/// For the WebViewCapability module to work, you must enable the SwizzleCapability module.
/// requires:
/// Supported on devices with iOS 9+.
  CapabilityWebView = 4,
/// NetworkCapability
/// The module receives the ssid and bssid parameters of the WiFi network to which the device is connected.
/// attention:
/// The module works if in the device settings the user has given the application permission to access geolocation data, the protected application is an active VPN application, or the protected application has configured a WiFi network that the device uses through NEHotspotConfiguration. The Mobile SDK does not ask for this permission on its own.
/// important:
/// For the module to work, in Xcode on the Signing & Capabilities tab, enable the <em>Access WiFi information</em> option.
/// requires:
/// Supported on devices with iOS 9+.
  CapabilityNetwork = 5,
/// MotionCapability
/// The module logs data from the device’s accelerometer, gravity sensor and gyroscope (X, Y, Z coordinates).
/// important:
/// For the MotionCapability module to work, you must enable the BehaviorCapability module.
/// requires:
/// Supported on devices with iOS/tvOS 9+.
  CapabilityMotion = 6,
/// SwizzleCapability
/// <h2>The module performs run-time injection into methods:</h2>
/// warning:
/// <em>Module should be enabled before calling the run method. After enabling, you can disable this module only by completely restarting the application.</em>
/// requires:
/// Supported on devices with iOS/tvOS 9+.
/// <ul>
///   <li>
///     `UIViewController: viewDidAppear(_ animated: Bool)
///   </li>
///   <li>
///     <code>UIApplication: sendEvent(_ event: UIEvent)</code>
///   </li>
/// </ul>
  CapabilitySwizzle = 7,
/// LocationCapability
/// The module collects data about the device’s location: latitude, longitude, acc, alt, course, alt_acc, speed_acc (for devices with iOS 10.0+), speed.
/// important:
/// Mobile SDK does not receive geolocation data when the application is not in the foreground.
/// warning:
/// The module works if in the device settings the user has given the application permission to access geolocation data. Mobile SDK does not ask for this permission on its own.
/// requires:
/// Supported on devices with iOS/tvOS 9+.
  CapabilityLocation = 8,
/// AudioCapability
/// The module monitors the audio output source that is currently being used.
/// important:
/// Only the fact of audio playback and its source is logged. Mobile SDK does not access audio content.
/// requires:
/// Supported on devices with iOS/tvOS 9+.
  CapabilityAudio = 9,
/// CloudIdentifierCapability
/// The module transmits and stores in iCloud Key-value storage a unique value for the cloudGibId parameter. Used to identify devices via <a href="http://fhwiki.group-ib.tech/Start/cloud_id/">Cloud ID</a>
/// The module will transfer service data for identifying user device with Cloud ID only after the user logs into the Apple account.
/// important:
/// For the module to work, in Xcode on the <em>Signing & Capabilities</em> tab, enable the <em>iCloud Key-value storage</em> option.
/// requires:
/// Supported on devices with iOS/tvOS 9+.
  CapabilityCloudIdentifier = 10,
/// DeviceStatusCapability
/// The module receives and processes information about device being close to the ear (data from the Proximity sensor).
/// note:
/// Allows to receive more data if used with the <em>CallCapability</em> module.
/// requires:
/// Supported on devices with iOS/tvOS 9+.
  CapabilityDeviceStatus = 11,
/// CaptureCapability
/// The module registers saving a screenshot (for devices with <em>iOS/tvOS 9+</em>) and recording a video of the screen (for devices with <em>iOS 11+</em>).
/// important:
/// Only the fact of saving a screenshot or recording the screen is logged. Mobile SDK cannot access the content of files.
/// requires:
/// Supported on devices with iOS 9+, tvOS 14+.
  CapabilityCapture = 12,
/// AppsCapability
/// The module receives and processes information about applications installed on the user’s device. Applications on the device are checked by calling the <em>UIApplication canOpenURL</em> method.
/// important:
/// For the module to work, add application keys to the Info.plist file of the protected application. To receive an up-to-date list of application keys, contact <a href="mailto:<EMAIL>">Group-IB specialists</a>.
/// requires:
/// Supported on devices with iOS/tvOS 9+.
  CapabilityApps = 13,
/// ProxyCapability
/// The module receives and processes information about the use of VPN or Proxy on the user’s device.
/// <h2>The module gets access to the parameters:</h2>
/// requires:
/// Supported on devices with iOS/tvOS 9+.
/// <ul>
///   <li>
///     http proxy port,
///   </li>
///   <li>
///     http proxy address,
///   </li>
///   <li>
///     http proxy user,
///   </li>
///   <li>
///     VPN protocol.
///   </li>
/// </ul>
  CapabilityProxy = 14,
/// KeyboardCapability
/// The module receives and processes information about keyboards on a mobile device (identifiers of installed software keyboards).
/// requires:
/// Supported on devices with iOS/tvOS 9+.
  CapabilityKeyboard = 15,
/// BehaviorCapability
/// The module is used to collect data about user behavior in the application: screen clicks, swipes, opening new UIViewController objects, UIView elements on the screen and their location, typing speed in UITextField and UITextView.
/// note:
/// <em>Typing speed in a UITextField with isSecure token is not logged.</em>
/// important:
/// For the BehaviorCapability module to work, you must enable the SwizzleCapability module.
/// requires:
/// Supported on devices with iOS/tvOS 9+.
  CapabilityBehavior = 16,
/// PreventScreenshotsCapability
/// This module can auto prevent take screenshots by user. Work for screenshots, screensharing, remote control, Xcode screenshots.
  CapabilityPreventScreenshots = 17,
/// SecurityCapability
/// This module can detect jailbreak on device
  CapabilitySecurity = 18,
/// AdvertiseCapability
/// The module gets the IDFA number from the user’s device if the user has given permission.
/// important:
/// The module don’t request permission from user.
  CapabilityAdvertise = 19,
  CapabilityPortScan = 20,
  CapabilityGlobalId = 21,
};



enum LogType : NSInteger;
@class WKWebView;

/// Internal.
/// warning:
/// DO NOT USE.
SWIFT_PROTOCOL("_TtP12GIBMobileSdk23GIBSwiftManagerDelegate_")
@protocol GIBSwiftManagerDelegate
/// Internal.
/// warning:
/// DO NOT USE.
- (void)sendDictionaryFromSwiftWithDictionary:(NSDictionary * _Nonnull)dictionary isRepeatData:(BOOL)isRepeatData;
/// Internal.
/// warning:
/// DO NOT USE.
- (void)sendLog:(NSString * _Nonnull)log type:(enum LogType)type;
/// Internal.
/// warning:
/// DO NOT USE.
- (void)sendInitAppWebView:(WKWebView * _Nonnull)webView;
@end

@class WKNavigation;
@class WKNavigationAction;
@class WKWebpagePreferences;
@class WKNavigationResponse;
@class NSURLAuthenticationChallenge;
@class NSURLCredential;
@class WKDownload;

/// The <em>GIBWebViewNavigationDelegate</em> class implements methods of the WKNavigationDelegate protocol to track new web pages opened within the application and invoke JavaScript module methods on them.
SWIFT_CLASS("_TtC12GIBMobileSdk28GIBWebViewNavigationDelegate")
@interface GIBWebViewNavigationDelegate : NSObject <WKNavigationDelegate>
/// Standard initialization
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
/// Initialization with the possibility to transmit custom implementation of the WKNavigationDelegate protocol.
/// In this case, the GIBWebViewNavigationDelegate will proxy the transmitted methods into its implementation.
/// \param navigationDelegate Object of the WKNavigationDelegate protocol implementation
///
- (nonnull instancetype)initWithNavigationDelegate:(id <WKNavigationDelegate> _Nullable)navigationDelegate OBJC_DESIGNATED_INITIALIZER;
- (void)webView:(WKWebView * _Nonnull)webView didFinishNavigation:(WKNavigation * _Null_unspecified)navigation;
- (void)webView:(WKWebView * _Nonnull)webView decidePolicyForNavigationAction:(WKNavigationAction * _Nonnull)navigationAction decisionHandler:(void (^ _Nonnull)(WKNavigationActionPolicy))decisionHandler;
- (void)webView:(WKWebView * _Nonnull)webView decidePolicyForNavigationAction:(WKNavigationAction * _Nonnull)navigationAction preferences:(WKWebpagePreferences * _Nonnull)preferences decisionHandler:(void (^ _Nonnull)(WKNavigationActionPolicy, WKWebpagePreferences * _Nonnull))decisionHandler SWIFT_AVAILABILITY(ios,introduced=13.0);
- (void)webView:(WKWebView * _Nonnull)webView decidePolicyForNavigationResponse:(WKNavigationResponse * _Nonnull)navigationResponse decisionHandler:(void (^ _Nonnull)(WKNavigationResponsePolicy))decisionHandler;
- (void)webView:(WKWebView * _Nonnull)webView didStartProvisionalNavigation:(WKNavigation * _Null_unspecified)navigation;
- (void)webView:(WKWebView * _Nonnull)webView didReceiveServerRedirectForProvisionalNavigation:(WKNavigation * _Null_unspecified)navigation;
- (void)webView:(WKWebView * _Nonnull)webView didFailProvisionalNavigation:(WKNavigation * _Null_unspecified)navigation withError:(NSError * _Nonnull)error;
- (void)webView:(WKWebView * _Nonnull)webView didCommitNavigation:(WKNavigation * _Null_unspecified)navigation;
- (void)webView:(WKWebView * _Nonnull)webView didFailNavigation:(WKNavigation * _Null_unspecified)navigation withError:(NSError * _Nonnull)error;
- (void)webView:(WKWebView * _Nonnull)webView didReceiveAuthenticationChallenge:(NSURLAuthenticationChallenge * _Nonnull)challenge completionHandler:(void (^ _Nonnull)(NSURLSessionAuthChallengeDisposition, NSURLCredential * _Nullable))completionHandler;
- (void)webViewWebContentProcessDidTerminate:(WKWebView * _Nonnull)webView;
- (void)webView:(WKWebView * _Nonnull)webView authenticationChallenge:(NSURLAuthenticationChallenge * _Nonnull)challenge shouldAllowDeprecatedTLS:(void (^ _Nonnull)(BOOL))decisionHandler SWIFT_AVAILABILITY(ios,introduced=14.0);
- (void)webView:(WKWebView * _Nonnull)webView navigationAction:(WKNavigationAction * _Nonnull)navigationAction didBecomeDownload:(WKDownload * _Nonnull)download SWIFT_AVAILABILITY(ios,introduced=14.5);
- (void)webView:(WKWebView * _Nonnull)webView navigationResponse:(WKNavigationResponse * _Nonnull)navigationResponse didBecomeDownload:(WKDownload * _Nonnull)download SWIFT_AVAILABILITY(ios,introduced=14.5);
@end


SWIFT_CLASS("_TtC12GIBMobileSdk15LocationService")
@interface LocationService : NSObject
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

@class CLLocationManager;
@class CLLocation;

@interface LocationService (SWIFT_EXTENSION(GIBMobileSdk)) <CLLocationManagerDelegate>
- (void)locationManager:(CLLocationManager * _Nonnull)manager didUpdateLocations:(NSArray<CLLocation *> * _Nonnull)locations;
- (void)locationManagerDidChangeAuthorization:(CLLocationManager * _Nonnull)manager;
- (void)locationManager:(CLLocationManager * _Nonnull)manager didFailWithError:(NSError * _Nonnull)error;
@end

/// Log types of SDK
typedef SWIFT_ENUM(NSInteger, LogType, open) {
/// Log-message with error
  LogTypeError = 0,
/// Log-message with warning
  LogTypeWarning = 1,
/// Info message
  LogTypeInfo = 2,
/// Verbosed log-message from Mobile SDK
  LogTypeVerbose = 3,
};





@interface UIView (SWIFT_EXTENSION(GIBMobileSdk))
- (void)fpPinLayout:(enum NSLayoutAttribute)type;
- (void)fpPinLayoutEdges;
@end




@interface UIWindow (SWIFT_EXTENSION(GIBMobileSdk))
/// Use this method for hide information on the UIWindow when user take screenshots, screensharing, remote control and Xcode screenshots
- (void)gibPreventScreenshots;
@end


@interface UIWindow (SWIFT_EXTENSION(GIBMobileSdk))
- (void)capabilityPreventScreenshots;
- (void)capabilityDisablePreventScreenshots;
@end


SWIFT_CLASS("_TtC12GIBMobileSdk13_FPSecureView")
@interface _FPSecureView : UITextField
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)coder OBJC_DESIGNATED_INITIALIZER;
@property (nonatomic, readonly, strong) UIView * _Nullable secureContainer;
@property (nonatomic, readonly) BOOL canBecomeFirstResponder;
- (BOOL)becomeFirstResponder SWIFT_WARN_UNUSED_RESULT;
@end

#endif
#if __has_attribute(external_source_symbol)
# pragma clang attribute pop
#endif
#if defined(__cplusplus)
#endif
#pragma clang diagnostic pop
#endif

#else
#error unsupported Swift architecture
#endif
