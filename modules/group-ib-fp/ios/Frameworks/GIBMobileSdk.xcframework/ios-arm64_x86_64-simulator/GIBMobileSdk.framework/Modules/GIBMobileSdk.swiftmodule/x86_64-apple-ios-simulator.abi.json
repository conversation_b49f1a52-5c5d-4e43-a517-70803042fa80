{"ABIRoot": {"kind": "Root", "name": "GIBMobileSdk", "printedName": "GIBMobileSdk", "children": [{"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "MachO", "printedName": "MachO", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "TypeDecl", "name": "GIBHelperManager", "printedName": "GIBHelperManager", "children": [{"kind": "Var", "name": "getTimeOfDay", "printedName": "getTimeOfDay", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "declKind": "Var", "usr": "s:12GIBMobileSdk16GIBHelperManagerC12getTimeOfDaySdvpZ", "mangledName": "$s12GIBMobileSdk16GIBHelperManagerC12getTimeOfDaySdvpZ", "moduleName": "GIBMobileSdk", "static": true, "declAttributes": ["Final", "AccessControl"], "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "declKind": "Accessor", "usr": "s:12GIBMobileSdk16GIBHelperManagerC12getTimeOfDaySdvgZ", "mangledName": "$s12GIBMobileSdk16GIBHelperManagerC12getTimeOfDaySdvgZ", "moduleName": "GIBMobileSdk", "static": true, "declAttributes": ["Final"], "accessorKind": "get"}]}], "declKind": "Class", "usr": "s:12GIBMobileSdk16GIBHelperManagerC", "mangledName": "$s12GIBMobileSdk16GIBHelperManagerC", "moduleName": "GIBMobileSdk", "declAttributes": ["Final", "AccessControl"], "hasMissingDesignatedInitializers": true, "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "ExternalAccessory", "printedName": "ExternalAccessory", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "TypeDecl", "name": "GIBCapability", "printedName": "GIBCapability", "children": [{"kind": "Var", "name": "shared", "printedName": "shared", "children": [{"kind": "TypeNominal", "name": "GIBCapability", "printedName": "GIBMobileSdk.GIBCapability", "usr": "s:12GIBMobileSdk13GIBCapabilityC"}], "declKind": "Var", "usr": "s:12GIBMobileSdk13GIBCapabilityC6sharedACvpZ", "mangledName": "$s12GIBMobileSdk13GIBCapabilityC6sharedACvpZ", "moduleName": "GIBMobileSdk", "static": true, "isOpen": true, "declAttributes": ["AccessControl"], "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "GIBCapability", "printedName": "GIBMobileSdk.GIBCapability", "usr": "s:12GIBMobileSdk13GIBCapabilityC"}], "declKind": "Accessor", "usr": "s:12GIBMobileSdk13GIBCapabilityC6sharedACvgZ", "mangledName": "$s12GIBMobileSdk13GIBCapabilityC6sharedACvgZ", "moduleName": "GIBMobileSdk", "static": true, "isOpen": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "isRun", "printedName": "isRun", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "declKind": "Var", "usr": "s:12GIBMobileSdk13GIBCapabilityC5isRunSbvp", "mangledName": "$s12GIBMobileSdk13GIBCapabilityC5isRunSbvp", "moduleName": "GIBMobileSdk", "declAttributes": ["HasInitialValue", "HasStorage", "SetterAccess", "AccessControl"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "declKind": "Accessor", "usr": "s:12GIBMobileSdk13GIBCapabilityC5isRunSbvg", "mangledName": "$s12GIBMobileSdk13GIBCapabilityC5isRunSbvg", "moduleName": "GIBMobileSdk", "implicit": true, "accessorKind": "get"}]}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init()", "children": [{"kind": "TypeNominal", "name": "GIBCapability", "printedName": "GIBMobileSdk.GIBCapability", "usr": "s:12GIBMobileSdk13GIBCapabilityC"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:12GIBMobileSdk13GIBCapabilityCACycfc", "mangledName": "$s12GIBMobileSdk13GIBCapabilityCACycfc", "moduleName": "GIBMobileSdk", "declAttributes": ["AccessControl"], "init_kind": "Designated"}, {"kind": "Function", "name": "run", "printedName": "run()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Func", "usr": "s:12GIBMobileSdk13GIBCapabilityC3runyyKF", "mangledName": "$s12GIBMobileSdk13GIBCapabilityC3runyyKF", "moduleName": "GIBMobileSdk", "isOpen": true, "declAttributes": ["AccessControl"], "throwing": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "stop", "printedName": "stop()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Func", "usr": "s:12GIBMobileSdk13GIBCapabilityC4stopyyKF", "mangledName": "$s12GIBMobileSdk13GIBCapabilityC4stopyyKF", "moduleName": "GIBMobileSdk", "isOpen": true, "declAttributes": ["AccessControl"], "throwing": true, "funcSelfKind": "NonMutating"}], "declKind": "Class", "usr": "s:12GIBMobileSdk13GIBCapabilityC", "mangledName": "$s12GIBMobileSdk13GIBCapabilityC", "moduleName": "GIBMobileSdk", "isOpen": true, "declAttributes": ["AccessControl", "RawDocComment"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "CoreTelephony", "printedName": "CoreTelephony", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "LocalAuthentication", "printedName": "LocalAuthentication", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "MachO.arch", "printedName": "MachO.arch", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "MachO", "printedName": "MachO", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "TypeDecl", "name": "GIBPacket", "printedName": "GIBPacket", "children": [{"kind": "TypeDecl", "name": "Versions", "printedName": "Versions", "children": [{"kind": "Var", "name": "v1_0_0", "printedName": "v1_0_0", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBPacket.Versions.Type) -> GIBMobileSdk.GIBPacket.Versions", "children": [{"kind": "TypeNominal", "name": "Versions", "printedName": "GIBMobileSdk.GIBPacket.Versions", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBPacket.Versions.Type", "children": [{"kind": "TypeNominal", "name": "Versions", "printedName": "GIBMobileSdk.GIBPacket.Versions", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO6v1_0_0yA2EmF", "mangledName": "$s12GIBMobileSdk9GIBPacketC8VersionsO6v1_0_0yA2EmF", "moduleName": "GIBMobileSdk"}, {"kind": "Var", "name": "v1_1_0", "printedName": "v1_1_0", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBPacket.Versions.Type) -> GIBMobileSdk.GIBPacket.Versions", "children": [{"kind": "TypeNominal", "name": "Versions", "printedName": "GIBMobileSdk.GIBPacket.Versions", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBPacket.Versions.Type", "children": [{"kind": "TypeNominal", "name": "Versions", "printedName": "GIBMobileSdk.GIBPacket.Versions", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO6v1_1_0yA2EmF", "mangledName": "$s12GIBMobileSdk9GIBPacketC8VersionsO6v1_1_0yA2EmF", "moduleName": "GIBMobileSdk"}, {"kind": "Var", "name": "v2_0_0", "printedName": "v2_0_0", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBPacket.Versions.Type) -> GIBMobileSdk.GIBPacket.Versions", "children": [{"kind": "TypeNominal", "name": "Versions", "printedName": "GIBMobileSdk.GIBPacket.Versions", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBPacket.Versions.Type", "children": [{"kind": "TypeNominal", "name": "Versions", "printedName": "GIBMobileSdk.GIBPacket.Versions", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO6v2_0_0yA2EmF", "mangledName": "$s12GIBMobileSdk9GIBPacketC8VersionsO6v2_0_0yA2EmF", "moduleName": "GIBMobileSdk"}, {"kind": "Var", "name": "v2_1_0", "printedName": "v2_1_0", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBPacket.Versions.Type) -> GIBMobileSdk.GIBPacket.Versions", "children": [{"kind": "TypeNominal", "name": "Versions", "printedName": "GIBMobileSdk.GIBPacket.Versions", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBPacket.Versions.Type", "children": [{"kind": "TypeNominal", "name": "Versions", "printedName": "GIBMobileSdk.GIBPacket.Versions", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO6v2_1_0yA2EmF", "mangledName": "$s12GIBMobileSdk9GIBPacketC8VersionsO6v2_1_0yA2EmF", "moduleName": "GIBMobileSdk"}, {"kind": "Var", "name": "v3_0_0", "printedName": "v3_0_0", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBPacket.Versions.Type) -> GIBMobileSdk.GIBPacket.Versions", "children": [{"kind": "TypeNominal", "name": "Versions", "printedName": "GIBMobileSdk.GIBPacket.Versions", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBPacket.Versions.Type", "children": [{"kind": "TypeNominal", "name": "Versions", "printedName": "GIBMobileSdk.GIBPacket.Versions", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO6v3_0_0yA2EmF", "mangledName": "$s12GIBMobileSdk9GIBPacketC8VersionsO6v3_0_0yA2EmF", "moduleName": "GIBMobileSdk"}, {"kind": "Var", "name": "v3_1_0", "printedName": "v3_1_0", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBPacket.Versions.Type) -> GIBMobileSdk.GIBPacket.Versions", "children": [{"kind": "TypeNominal", "name": "Versions", "printedName": "GIBMobileSdk.GIBPacket.Versions", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBPacket.Versions.Type", "children": [{"kind": "TypeNominal", "name": "Versions", "printedName": "GIBMobileSdk.GIBPacket.Versions", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO6v3_1_0yA2EmF", "mangledName": "$s12GIBMobileSdk9GIBPacketC8VersionsO6v3_1_0yA2EmF", "moduleName": "GIBMobileSdk"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(rawValue:)", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "GIBMobileSdk.GIBPacket.Versions?", "children": [{"kind": "TypeNominal", "name": "Versions", "printedName": "GIBMobileSdk.GIBPacket.Versions", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO8rawValueAESgSS_tcfc", "mangledName": "$s12GIBMobileSdk9GIBPacketC8VersionsO8rawValueAESgSS_tcfc", "moduleName": "GIBMobileSdk", "implicit": true, "init_kind": "Designated"}, {"kind": "Var", "name": "rawValue", "printedName": "rawValue", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO8rawValueSSvp", "mangledName": "$s12GIBMobileSdk9GIBPacketC8VersionsO8rawValueSSvp", "moduleName": "GIBMobileSdk", "implicit": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO8rawValueSSvg", "mangledName": "$s12GIBMobileSdk9GIBPacketC8VersionsO8rawValueSSvg", "moduleName": "GIBMobileSdk", "implicit": true, "accessorKind": "get"}]}], "declKind": "Enum", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO", "mangledName": "$s12GIBMobileSdk9GIBPacketC8VersionsO", "moduleName": "GIBMobileSdk", "declAttributes": ["AccessControl"], "enumRawTypeName": "String", "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "RawRepresentable", "printedName": "RawRepresentable", "children": [{"kind": "TypeWitness", "name": "RawValue", "printedName": "RawValue", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}]}], "usr": "s:SY", "mangledName": "$sSY"}]}, {"kind": "TypeDecl", "name": "Providers", "printedName": "Providers", "children": [{"kind": "Var", "name": "advertise", "printedName": "advertise", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBPacket.Providers.Type) -> GIBMobileSdk.GIBPacket.Providers", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBPacket.Providers.Type", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO9advertiseyA2EmF", "mangledName": "$s12GIBMobileSdk9GIBPacketC9ProvidersO9advertiseyA2EmF", "moduleName": "GIBMobileSdk"}, {"kind": "Var", "name": "cellular", "printedName": "cellular", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBPacket.Providers.Type) -> GIBMobileSdk.GIBPacket.Providers", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBPacket.Providers.Type", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO8cellularyA2EmF", "mangledName": "$s12GIBMobileSdk9GIBPacketC9ProvidersO8cellularyA2EmF", "moduleName": "GIBMobileSdk"}, {"kind": "Var", "name": "network", "printedName": "network", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBPacket.Providers.Type) -> GIBMobileSdk.GIBPacket.Providers", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBPacket.Providers.Type", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO7networkyA2EmF", "mangledName": "$s12GIBMobileSdk9GIBPacketC9ProvidersO7networkyA2EmF", "moduleName": "GIBMobileSdk"}, {"kind": "Var", "name": "location", "printedName": "location", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBPacket.Providers.Type) -> GIBMobileSdk.GIBPacket.Providers", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBPacket.Providers.Type", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO8locationyA2EmF", "mangledName": "$s12GIBMobileSdk9GIBPacketC9ProvidersO8locationyA2EmF", "moduleName": "GIBMobileSdk"}, {"kind": "Var", "name": "cloud", "printedName": "cloud", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBPacket.Providers.Type) -> GIBMobileSdk.GIBPacket.Providers", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBPacket.Providers.Type", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO5cloudyA2EmF", "mangledName": "$s12GIBMobileSdk9GIBPacketC9ProvidersO5cloudyA2EmF", "moduleName": "GIBMobileSdk"}, {"kind": "Var", "name": "behavior", "printedName": "behavior", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBPacket.Providers.Type) -> GIBMobileSdk.GIBPacket.Providers", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBPacket.Providers.Type", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO8behavioryA2EmF", "mangledName": "$s12GIBMobileSdk9GIBPacketC9ProvidersO8behavioryA2EmF", "moduleName": "GIBMobileSdk"}, {"kind": "Var", "name": "auth", "printedName": "auth", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBPacket.Providers.Type) -> GIBMobileSdk.GIBPacket.Providers", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBPacket.Providers.Type", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO4authyA2EmF", "mangledName": "$s12GIBMobileSdk9GIBPacketC9ProvidersO4authyA2EmF", "moduleName": "GIBMobileSdk"}, {"kind": "Var", "name": "capture", "printedName": "capture", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBPacket.Providers.Type) -> GIBMobileSdk.GIBPacket.Providers", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBPacket.Providers.Type", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO7captureyA2EmF", "mangledName": "$s12GIBMobileSdk9GIBPacketC9ProvidersO7captureyA2EmF", "moduleName": "GIBMobileSdk"}, {"kind": "Var", "name": "deviceStatus", "printedName": "deviceStatus", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBPacket.Providers.Type) -> GIBMobileSdk.GIBPacket.Providers", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBPacket.Providers.Type", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO12deviceStatusyA2EmF", "mangledName": "$s12GIBMobileSdk9GIBPacketC9ProvidersO12deviceStatusyA2EmF", "moduleName": "GIBMobileSdk"}, {"kind": "Var", "name": "audio", "printedName": "audio", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBPacket.Providers.Type) -> GIBMobileSdk.GIBPacket.Providers", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBPacket.Providers.Type", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO5audioyA2EmF", "mangledName": "$s12GIBMobileSdk9GIBPacketC9ProvidersO5audioyA2EmF", "moduleName": "GIBMobileSdk"}, {"kind": "Var", "name": "battery", "printedName": "battery", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBPacket.Providers.Type) -> GIBMobileSdk.GIBPacket.Providers", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBPacket.Providers.Type", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO7batteryyA2EmF", "mangledName": "$s12GIBMobileSdk9GIBPacketC9ProvidersO7batteryyA2EmF", "moduleName": "GIBMobileSdk"}, {"kind": "Var", "name": "proxy", "printedName": "proxy", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBPacket.Providers.Type) -> GIBMobileSdk.GIBPacket.Providers", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBPacket.Providers.Type", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO5proxyyA2EmF", "mangledName": "$s12GIBMobileSdk9GIBPacketC9ProvidersO5proxyyA2EmF", "moduleName": "GIBMobileSdk"}, {"kind": "Var", "name": "keyboard", "printedName": "keyboard", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBPacket.Providers.Type) -> GIBMobileSdk.GIBPacket.Providers", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBPacket.Providers.Type", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO8keyboardyA2EmF", "mangledName": "$s12GIBMobileSdk9GIBPacketC9ProvidersO8keyboardyA2EmF", "moduleName": "GIBMobileSdk"}, {"kind": "Var", "name": "security", "printedName": "security", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBPacket.Providers.Type) -> GIBMobileSdk.GIBPacket.Providers", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBPacket.Providers.Type", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO8securityyA2EmF", "mangledName": "$s12GIBMobileSdk9GIBPacketC9ProvidersO8securityyA2EmF", "moduleName": "GIBMobileSdk"}, {"kind": "Var", "name": "portScan", "printedName": "portScan", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBPacket.Providers.Type) -> GIBMobileSdk.GIBPacket.Providers", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBPacket.Providers.Type", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO8portScanyA2EmF", "mangledName": "$s12GIBMobileSdk9GIBPacketC9ProvidersO8portScanyA2EmF", "moduleName": "GIBMobileSdk"}, {"kind": "Var", "name": "globalId", "printedName": "globalId", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBPacket.Providers.Type) -> GIBMobileSdk.GIBPacket.Providers", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBPacket.Providers.Type", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO8globalIdyA2EmF", "mangledName": "$s12GIBMobileSdk9GIBPacketC9ProvidersO8globalIdyA2EmF", "moduleName": "GIBMobileSdk"}, {"kind": "Var", "name": "custom", "printedName": "custom", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBPacket.Providers.Type) -> (Swift.String) -> GIBMobileSdk.GIBPacket.Providers", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(Swift.String) -> GIBMobileSdk.GIBPacket.Providers", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}]}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBPacket.Providers.Type", "children": [{"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO6customyAESScAEmF", "mangledName": "$s12GIBMobileSdk9GIBPacketC9ProvidersO6customyAESScAEmF", "moduleName": "GIBMobileSdk"}], "declKind": "Enum", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO", "mangledName": "$s12GIBMobileSdk9GIBPacketC9ProvidersO", "moduleName": "GIBMobileSdk", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "Var", "name": "packetDictionary", "printedName": "packetDictionary", "children": [{"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.String : Any]", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}], "declKind": "Var", "usr": "s:12GIBMobileSdk9GIBPacketC16packetDictionarySDySSypGvp", "mangledName": "$s12GIBMobileSdk9GIBPacketC16packetDictionarySDySSypGvp", "moduleName": "GIBMobileSdk", "declAttributes": ["Final", "AccessControl"], "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.String : Any]", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}], "declKind": "Accessor", "usr": "s:12GIBMobileSdk9GIBPacketC16packetDictionarySDySSypGvg", "mangledName": "$s12GIBMobileSdk9GIBPacketC16packetDictionarySDySSypGvg", "moduleName": "GIBMobileSdk", "declAttributes": ["Final"], "accessorKind": "get"}]}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(version:provider:send:data:)", "children": [{"kind": "TypeNominal", "name": "GIBPacket", "printedName": "GIBMobileSdk.GIBPacket", "usr": "s:12GIBMobileSdk9GIBPacketC"}, {"kind": "TypeNominal", "name": "Versions", "printedName": "GIBMobileSdk.GIBPacket.Versions", "usr": "s:12GIBMobileSdk9GIBPacketC8VersionsO"}, {"kind": "TypeNominal", "name": "Providers", "printedName": "GIBMobileSdk.GIBPacket.Providers", "usr": "s:12GIBMobileSdk9GIBPacketC9ProvidersO"}, {"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "hasDefaultArg": true, "usr": "s:Sb"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:12GIBMobileSdk9GIBPacketC7version8provider4send4dataA2C8VersionsO_AC9ProvidersOSbyptcfc", "mangledName": "$s12GIBMobileSdk9GIBPacketC7version8provider4send4dataA2C8VersionsO_AC9ProvidersOSbyptcfc", "moduleName": "GIBMobileSdk", "declAttributes": ["AccessControl"], "init_kind": "Designated"}], "declKind": "Class", "usr": "s:12GIBMobileSdk9GIBPacketC", "mangledName": "$s12GIBMobileSdk9GIBPacketC", "moduleName": "GIBMobileSdk", "declAttributes": ["Final", "AccessControl"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "TypeDecl", "name": "GIBModelProtocol", "printedName": "GIBModelProtocol", "children": [{"kind": "Var", "name": "dictionary", "printedName": "dictionary", "children": [{"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.String : Any]", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}], "declKind": "Var", "usr": "s:12GIBMobileSdk16GIBModelProtocolP10dictionarySDySSypGvp", "mangledName": "$s12GIBMobileSdk16GIBModelProtocolP10dictionarySDySSypGvp", "moduleName": "GIBMobileSdk", "protocolReq": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.String : Any]", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}], "declKind": "Accessor", "usr": "s:12GIBMobileSdk16GIBModelProtocolP10dictionarySDySSypGvg", "mangledName": "$s12GIBMobileSdk16GIBModelProtocolP10dictionarySDySSypGvg", "moduleName": "GIBMobileSdk", "genericSig": "<τ_0_0 where τ_0_0 : GIBMobileSdk.GIBModelProtocol>", "sugared_genericSig": "<Self where Self : GIBMobileSdk.GIBModelProtocol>", "protocolReq": true, "reqNewWitnessTableEntry": true, "accessorKind": "get"}]}], "declKind": "Protocol", "usr": "s:12GIBMobileSdk16GIBModelProtocolP", "mangledName": "$s12GIBMobileSdk16GIBModelProtocolP", "moduleName": "GIBMobileSdk", "genericSig": "<τ_0_0 : <PERSON><PERSON>>", "sugared_genericSig": "<Self : <PERSON><PERSON>>", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "MachO", "printedName": "MachO", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "CoreLocation", "printedName": "CoreLocation", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "WebKit", "printedName": "WebKit", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "TypeDecl", "name": "BehaviorManager", "printedName": "BehaviorManager", "children": [{"kind": "Var", "name": "isExtendedData", "printedName": "isExtendedData", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "declKind": "Var", "usr": "c:@M@GIBMobileSdk@objc(cs)BehaviorManager(cpy)isExtendedData", "mangledName": "$s12GIBMobileSdk15BehaviorManagerC14isExtendedDataSbvpZ", "moduleName": "GIBMobileSdk", "static": true, "declAttributes": ["HasInitialValue", "Final", "HasStorage", "AccessControl", "ObjC"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "declKind": "Accessor", "usr": "c:@M@GIBMobileSdk@objc(cs)BehaviorManager(cm)isExtendedData", "mangledName": "$s12GIBMobileSdk15BehaviorManagerC14isExtendedDataSbvgZ", "moduleName": "GIBMobileSdk", "static": true, "implicit": true, "declAttributes": ["Final", "ObjC"], "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "declKind": "Accessor", "usr": "c:@M@GIBMobileSdk@objc(cs)BehaviorManager(cm)setIsExtendedData:", "mangledName": "$s12GIBMobileSdk15BehaviorManagerC14isExtendedDataSbvsZ", "moduleName": "GIBMobileSdk", "static": true, "implicit": true, "declAttributes": ["Final", "ObjC"], "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:12GIBMobileSdk15BehaviorManagerC14isExtendedDataSbvMZ", "mangledName": "$s12GIBMobileSdk15BehaviorManagerC14isExtendedDataSbvMZ", "moduleName": "GIBMobileSdk", "static": true, "implicit": true, "declAttributes": ["Final"], "accessorKind": "_modify"}]}], "declKind": "Class", "usr": "c:@M@GIBMobileSdk@objc(cs)BehaviorManager", "mangledName": "$s12GIBMobileSdk15BehaviorManagerC", "moduleName": "GIBMobileSdk", "declAttributes": ["Final", "AccessControl", "ObjC"], "superclassUsr": "c:objc(cs)NSObject", "hasMissingDesignatedInitializers": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "<PERSON>", "printedName": "<PERSON>", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "MachO", "printedName": "MachO", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "ObjectiveC", "printedName": "ObjectiveC", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "GameController", "printedName": "GameController", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "SystemConfiguration.CaptiveNetwork", "printedName": "SystemConfiguration.CaptiveNetwork", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "Network", "printedName": "Network", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "AVFoundation", "printedName": "AVFoundation", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "os", "printedName": "os", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Security", "printedName": "Security", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "AppTrackingTransparency", "printedName": "AppTrackingTransparency", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "AdSupport", "printedName": "AdSupport", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "TypeDecl", "name": "GIBApplication", "printedName": "GIBApplication", "children": [{"kind": "Var", "name": "shared", "printedName": "shared", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIApplication?", "children": [{"kind": "TypeNominal", "name": "UIApplication", "printedName": "UIKit.UIApplication", "usr": "c:objc(cs)UIApplication"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:12GIBMobileSdk14GIBApplicationC6sharedSo13UIApplicationCSgvpZ", "mangledName": "$s12GIBMobileSdk14GIBApplicationC6sharedSo13UIApplicationCSgvpZ", "moduleName": "GIBMobileSdk", "static": true, "declAttributes": ["Final", "AccessControl"], "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIApplication?", "children": [{"kind": "TypeNominal", "name": "UIApplication", "printedName": "UIKit.UIApplication", "usr": "c:objc(cs)UIApplication"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:12GIBMobileSdk14GIBApplicationC6sharedSo13UIApplicationCSgvgZ", "mangledName": "$s12GIBMobileSdk14GIBApplicationC6sharedSo13UIApplicationCSgvgZ", "moduleName": "GIBMobileSdk", "static": true, "declAttributes": ["Final"], "accessorKind": "get"}]}], "declKind": "Class", "usr": "s:12GIBMobileSdk14GIBApplicationC", "mangledName": "$s12GIBMobileSdk14GIBApplicationC", "moduleName": "GIBMobileSdk", "declAttributes": ["Final", "AccessControl"], "hasMissingDesignatedInitializers": true, "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "TypeDecl", "name": "DeviceStatusCapability", "printedName": "DeviceStatusCapability", "children": [{"kind": "Var", "name": "shared", "printedName": "shared", "children": [{"kind": "TypeNominal", "name": "DeviceStatusCapability", "printedName": "GIBMobileSdk.DeviceStatusCapability", "usr": "s:12GIBMobileSdk22DeviceStatusCapabilityC"}], "declKind": "Var", "usr": "s:12GIBMobileSdk22DeviceStatusCapabilityC6sharedACvpZ", "mangledName": "$s12GIBMobileSdk22DeviceStatusCapabilityC6sharedACvpZ", "moduleName": "GIBMobileSdk", "static": true, "overriding": true, "declAttributes": ["Final", "Override", "AccessControl"], "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "DeviceStatusCapability", "printedName": "GIBMobileSdk.DeviceStatusCapability", "usr": "s:12GIBMobileSdk22DeviceStatusCapabilityC"}], "declKind": "Accessor", "usr": "s:12GIBMobileSdk22DeviceStatusCapabilityC6sharedACvgZ", "mangledName": "$s12GIBMobileSdk22DeviceStatusCapabilityC6sharedACvgZ", "moduleName": "GIBMobileSdk", "static": true, "overriding": true, "declAttributes": ["Final"], "accessorKind": "get"}]}, {"kind": "Function", "name": "stop", "printedName": "stop()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Func", "usr": "s:12GIBMobileSdk22DeviceStatusCapabilityC4stopyyKF", "mangledName": "$s12GIBMobileSdk22DeviceStatusCapabilityC4stopyyKF", "moduleName": "GIBMobileSdk", "overriding": true, "declAttributes": ["Final", "Override", "AccessControl"], "throwing": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "enableProximityMonitoring", "printedName": "enableProximityMonitoring(_:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "declKind": "Func", "usr": "s:12GIBMobileSdk22DeviceStatusCapabilityC25enableProximityMonitoringyySbF", "mangledName": "$s12GIBMobileSdk22DeviceStatusCapabilityC25enableProximityMonitoringyySbF", "moduleName": "GIBMobileSdk", "declAttributes": ["Final", "AccessControl"], "funcSelfKind": "NonMutating"}], "declKind": "Class", "usr": "s:12GIBMobileSdk22DeviceStatusCapabilityC", "mangledName": "$s12GIBMobileSdk22DeviceStatusCapabilityC", "moduleName": "GIBMobileSdk", "declAttributes": ["Final", "AccessControl"], "superclassUsr": "s:12GIBMobileSdk13GIBCapabilityC", "hasMissingDesignatedInitializers": true, "inheritsConvenienceInitializers": true, "superclassNames": ["GIBMobileSdk.GIBCapability"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "CoreLocation", "printedName": "CoreLocation", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "WebKit", "printedName": "WebKit", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "AppTrackingTransparency", "printedName": "AppTrackingTransparency", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "TypeDecl", "name": "GIBSwiftManagerDelegate", "printedName": "GIBSwiftManagerDelegate", "children": [{"kind": "Function", "name": "sendDictionaryFromSwift", "printedName": "sendDictionaryFromSwift(dictionary:isRepeatData:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "NSDictionary", "printedName": "Foundation.NSDictionary", "usr": "c:objc(cs)NSDictionary"}, {"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(pl)GIBSwiftManagerDelegate(im)sendDictionaryFromSwiftWithDictionary:isRepeatData:", "mangledName": "$s12GIBMobileSdk23GIBSwiftManagerDelegateP23sendDictionaryFromSwift10dictionary12isRepeatDataySo12NSDictionaryC_SbtF", "moduleName": "GIBMobileSdk", "genericSig": "<τ_0_0 where τ_0_0 : GIBMobileSdk.GIBSwiftManagerDelegate>", "sugared_genericSig": "<Self where Self : GIBMobileSdk.GIBSwiftManagerDelegate>", "protocolReq": true, "objc_name": "sendDictionaryFromSwiftWithDictionary:isRepeatData:", "declAttributes": ["ObjC", "RawDocComment"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "sendLog", "printedName": "sendLog(_:type:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "LogType", "printedName": "GIBMobileSdk.LogType", "usr": "c:@M@GIBMobileSdk@E@LogType"}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(pl)GIBSwiftManagerDelegate(im)sendLog:type:", "mangledName": "$s12GIBMobileSdk23GIBSwiftManagerDelegateP7sendLog_4typeySS_AA0G4TypeOtF", "moduleName": "GIBMobileSdk", "genericSig": "<τ_0_0 where τ_0_0 : GIBMobileSdk.GIBSwiftManagerDelegate>", "sugared_genericSig": "<Self where Self : GIBMobileSdk.GIBSwiftManagerDelegate>", "protocolReq": true, "declAttributes": ["ObjC", "RawDocComment"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "sendInitAppWebView", "printedName": "sendInitAppWebView(_:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "WKWebView", "printedName": "WebKit.WKWebView", "usr": "c:objc(cs)WKWebView"}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(pl)GIBSwiftManagerDelegate(im)sendInitAppWebView:", "mangledName": "$s12GIBMobileSdk23GIBSwiftManagerDelegateP18sendInitAppWebViewyySo05WKWebJ0CF", "moduleName": "GIBMobileSdk", "genericSig": "<τ_0_0 where τ_0_0 : GIBMobileSdk.GIBSwiftManagerDelegate>", "sugared_genericSig": "<Self where Self : GIBMobileSdk.GIBSwiftManagerDelegate>", "protocolReq": true, "declAttributes": ["ObjC", "RawDocComment"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}], "declKind": "Protocol", "usr": "c:@M@GIBMobileSdk@objc(pl)GIBSwiftManagerDelegate", "mangledName": "$s12GIBMobileSdk23GIBSwiftManagerDelegateP", "moduleName": "GIBMobileSdk", "genericSig": "<τ_0_0 : AnyObject>", "sugared_genericSig": "<Self : AnyObject>", "declAttributes": ["AccessControl", "ObjC", "RawDocComment"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}]}, {"kind": "TypeDecl", "name": "GIBSwiftManager", "printedName": "GIBSwiftManager", "children": [{"kind": "TypeDecl", "name": "Capability", "printedName": "Capability", "children": [{"kind": "Var", "name": "batteryStatus", "printedName": "batteryStatus", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBSwiftManager.Capability.Type) -> GIBMobileSdk.GIBSwiftManager.Capability", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability.Type", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO13batteryStatusyA2EmF", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO13batteryStatusyA2EmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 0}, {"kind": "Var", "name": "cellular", "printedName": "cellular", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBSwiftManager.Capability.Type) -> GIBMobileSdk.GIBSwiftManager.Capability", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability.Type", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8cellularyA2EmF", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8cellularyA2EmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 1}, {"kind": "Var", "name": "call", "printedName": "call", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBSwiftManager.Capability.Type) -> GIBMobileSdk.GIBSwiftManager.Capability", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability.Type", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO4callyA2EmF", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO4callyA2EmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 2}, {"kind": "Var", "name": "passcode", "printedName": "passcode", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBSwiftManager.Capability.Type) -> GIBMobileSdk.GIBSwiftManager.Capability", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability.Type", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8passcodeyA2EmF", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8passcodeyA2EmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 3}, {"kind": "Var", "name": "webView", "printedName": "webView", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBSwiftManager.Capability.Type) -> GIBMobileSdk.GIBSwiftManager.Capability", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability.Type", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO7webViewyA2EmF", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO7webViewyA2EmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 4}, {"kind": "Var", "name": "network", "printedName": "network", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBSwiftManager.Capability.Type) -> GIBMobileSdk.GIBSwiftManager.Capability", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability.Type", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO7networkyA2EmF", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO7networkyA2EmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 5}, {"kind": "Var", "name": "motion", "printedName": "motion", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBSwiftManager.Capability.Type) -> GIBMobileSdk.GIBSwiftManager.Capability", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability.Type", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO6motionyA2EmF", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO6motionyA2EmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 6}, {"kind": "Var", "name": "swizzle", "printedName": "swizzle", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBSwiftManager.Capability.Type) -> GIBMobileSdk.GIBSwiftManager.Capability", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability.Type", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO7swizzleyA2EmF", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO7swizzleyA2EmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 7}, {"kind": "Var", "name": "location", "printedName": "location", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBSwiftManager.Capability.Type) -> GIBMobileSdk.GIBSwiftManager.Capability", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability.Type", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8locationyA2EmF", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8locationyA2EmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 8}, {"kind": "Var", "name": "audio", "printedName": "audio", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBSwiftManager.Capability.Type) -> GIBMobileSdk.GIBSwiftManager.Capability", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability.Type", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO5audioyA2EmF", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO5audioyA2EmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 9}, {"kind": "Var", "name": "cloudIdentifier", "printedName": "cloudIdentifier", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBSwiftManager.Capability.Type) -> GIBMobileSdk.GIBSwiftManager.Capability", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability.Type", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO15cloudIdentifieryA2EmF", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO15cloudIdentifieryA2EmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 10}, {"kind": "Var", "name": "deviceStatus", "printedName": "deviceStatus", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBSwiftManager.Capability.Type) -> GIBMobileSdk.GIBSwiftManager.Capability", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability.Type", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO12deviceStatusyA2EmF", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO12deviceStatusyA2EmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 11}, {"kind": "Var", "name": "capture", "printedName": "capture", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBSwiftManager.Capability.Type) -> GIBMobileSdk.GIBSwiftManager.Capability", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability.Type", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO7captureyA2EmF", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO7captureyA2EmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 12}, {"kind": "Var", "name": "apps", "printedName": "apps", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBSwiftManager.Capability.Type) -> GIBMobileSdk.GIBSwiftManager.Capability", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability.Type", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO4appsyA2EmF", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO4appsyA2EmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 13}, {"kind": "Var", "name": "proxy", "printedName": "proxy", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBSwiftManager.Capability.Type) -> GIBMobileSdk.GIBSwiftManager.Capability", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability.Type", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO5proxyyA2EmF", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO5proxyyA2EmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 14}, {"kind": "Var", "name": "keyboard", "printedName": "keyboard", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBSwiftManager.Capability.Type) -> GIBMobileSdk.GIBSwiftManager.Capability", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability.Type", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8keyboardyA2EmF", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8keyboardyA2EmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 15}, {"kind": "Var", "name": "behavior", "printedName": "behavior", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBSwiftManager.Capability.Type) -> GIBMobileSdk.GIBSwiftManager.Capability", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability.Type", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8behavioryA2EmF", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8behavioryA2EmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 16}, {"kind": "Var", "name": "preventScreenshots", "printedName": "preventScreenshots", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBSwiftManager.Capability.Type) -> GIBMobileSdk.GIBSwiftManager.Capability", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability.Type", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO18preventScreenshotsyA2EmF", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO18preventScreenshotsyA2EmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 17}, {"kind": "Var", "name": "security", "printedName": "security", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBSwiftManager.Capability.Type) -> GIBMobileSdk.GIBSwiftManager.Capability", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability.Type", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8securityyA2EmF", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8securityyA2EmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 18}, {"kind": "Var", "name": "advertise", "printedName": "advertise", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBSwiftManager.Capability.Type) -> GIBMobileSdk.GIBSwiftManager.Capability", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability.Type", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO9advertiseyA2EmF", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO9advertiseyA2EmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 19}, {"kind": "Var", "name": "portScan", "printedName": "portScan", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBSwiftManager.Capability.Type) -> GIBMobileSdk.GIBSwiftManager.Capability", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability.Type", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8portScanyA2EmF", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8portScanyA2EmF", "moduleName": "GIBMobileSdk", "declAttributes": ["ObjC"], "fixedbinaryorder": 20}, {"kind": "Var", "name": "globalId", "printedName": "globalId", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.GIBSwiftManager.Capability.Type) -> GIBMobileSdk.GIBSwiftManager.Capability", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability.Type", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8globalIdyA2EmF", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8globalIdyA2EmF", "moduleName": "GIBMobileSdk", "declAttributes": ["ObjC"], "fixedbinaryorder": 21}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(rawValue:)", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability?", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8rawValueAESgSi_tcfc", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8rawValueAESgSi_tcfc", "moduleName": "GIBMobileSdk", "implicit": true, "init_kind": "Designated"}, {"kind": "Var", "name": "rawValue", "printedName": "rawValue", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "declKind": "Var", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8rawValueSivp", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8rawValueSivp", "moduleName": "GIBMobileSdk", "implicit": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "declKind": "Accessor", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8rawValueSivg", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8rawValueSivg", "moduleName": "GIBMobileSdk", "implicit": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "allCases", "printedName": "allCases", "children": [{"kind": "TypeNominal", "name": "Array", "printedName": "[GIBMobileSdk.GIBSwiftManager.Capability]", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}], "usr": "s:Sa"}], "declKind": "Var", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8allCasesSayAEGvpZ", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8allCasesSayAEGvpZ", "moduleName": "GIBMobileSdk", "static": true, "implicit": true, "declAttributes": ["Nonisolated"], "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Array", "printedName": "[GIBMobileSdk.GIBSwiftManager.Capability]", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}], "usr": "s:Sa"}], "declKind": "Accessor", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8allCasesSayAEGvgZ", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO8allCasesSayAEGvgZ", "moduleName": "GIBMobileSdk", "static": true, "implicit": true, "accessorKind": "get"}]}], "declKind": "Enum", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC10CapabilityO", "moduleName": "GIBMobileSdk", "declAttributes": ["AccessControl", "ObjC", "RawDocComment"], "enumRawTypeName": "Int", "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "RawRepresentable", "printedName": "RawRepresentable", "children": [{"kind": "TypeWitness", "name": "RawValue", "printedName": "RawValue", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}]}], "usr": "s:SY", "mangledName": "$sSY"}, {"kind": "Conformance", "name": "CaseIterable", "printedName": "CaseIterable", "children": [{"kind": "TypeWitness", "name": "AllCases", "printedName": "AllCases", "children": [{"kind": "TypeNominal", "name": "Array", "printedName": "[GIBMobileSdk.GIBSwiftManager.Capability]", "children": [{"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}], "usr": "s:Sa"}]}], "usr": "s:s12CaseIterableP", "mangledName": "$ss12CaseIterableP"}]}, {"kind": "Var", "name": "shared", "printedName": "shared", "children": [{"kind": "TypeNominal", "name": "GIBSwiftManager", "printedName": "GIBMobileSdk.GIBSwiftManager", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBSwiftManager"}], "declKind": "Var", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBSwiftManager(cpy)shared", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC6sharedACvpZ", "moduleName": "GIBMobileSdk", "static": true, "declAttributes": ["HasInitialValue", "Final", "HasStorage", "AccessControl", "ObjC", "RawDocComment"], "isLet": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "GIBSwiftManager", "printedName": "GIBMobileSdk.GIBSwiftManager", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBSwiftManager"}], "declKind": "Accessor", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBSwiftManager(cm)shared", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC6sharedACvgZ", "moduleName": "GIBMobileSdk", "static": true, "implicit": true, "declAttributes": ["Final", "ObjC"], "accessorKind": "get"}]}, {"kind": "Var", "name": "delegate", "printedName": "delegate", "children": [{"kind": "TypeNominal", "name": "WeakStorage", "printedName": "(any GIBMobileSdk.GIBSwiftManagerDelegate)?"}], "declKind": "Var", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC8delegateAA0cD8Delegate_pSgvp", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC8delegateAA0cD8Delegate_pSgvp", "moduleName": "GIBMobileSdk", "declAttributes": ["HasInitialValue", "Final", "HasStorage", "ReferenceOwnership", "SetterAccess", "AccessControl", "RawDocComment"], "ownership": 1, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "(any GIBMobileSdk.GIBSwiftManagerDelegate)?", "children": [{"kind": "TypeNominal", "name": "GIBSwiftManagerDelegate", "printedName": "any GIBMobileSdk.GIBSwiftManagerDelegate", "usr": "c:@M@GIBMobileSdk@objc(pl)GIBSwiftManagerDelegate"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC8delegateAA0cD8Delegate_pSgvg", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC8delegateAA0cD8Delegate_pSgvg", "moduleName": "GIBMobileSdk", "implicit": true, "declAttributes": ["Final"], "accessorKind": "get"}]}, {"kind": "Function", "name": "enableCapability", "printedName": "enableCapability(_:)", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}, {"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBSwiftManager(im)enableCapability:", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC16enableCapabilityySbAC0F0OF", "moduleName": "GIBMobileSdk", "declAttributes": ["Final", "AccessControl", "ObjC", "DiscardableResult", "RawDocComment"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "disableCapability", "printedName": "disableCapability(_:)", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}, {"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBSwiftManager(im)disableCapability:", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC17disableCapabilityySbAC0F0OF", "moduleName": "GIBMobileSdk", "declAttributes": ["Final", "AccessControl", "ObjC", "DiscardableResult", "RawDocComment"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "run", "printedName": "run()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBSwiftManager(im)run", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC3runyyF", "moduleName": "GIBMobileSdk", "declAttributes": ["Final", "AccessControl", "ObjC", "RawDocComment"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "stop", "printedName": "stop()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBSwiftManager(im)stop", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC4stopyyF", "moduleName": "GIBMobileSdk", "declAttributes": ["Final", "AccessControl", "ObjC", "RawDocComment"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "isRun", "printedName": "isRun(_:)", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}, {"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBSwiftManager(im)isRun:", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC5isRunySbAC10CapabilityOF", "moduleName": "GIBMobileSdk", "declAttributes": ["Final", "AccessControl", "ObjC", "RawDocComment"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "allStatus", "printedName": "allStatus()", "children": [{"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.String : <PERSON><PERSON>]", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:SD"}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBSwiftManager(im)allStatus", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC9allStatusSDySSSbGyF", "moduleName": "GIBMobileSdk", "declAttributes": ["Final", "AccessControl", "ObjC", "RawDocComment"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "setOutput", "printedName": "setOutput(_:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "GIBSwiftManagerDelegate", "printedName": "any GIBMobileSdk.GIBSwiftManagerDelegate", "usr": "c:@M@GIBMobileSdk@objc(pl)GIBSwiftManagerDelegate"}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBSwiftManager(im)setOutput:", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC9setOutputyyAA0cD8Delegate_pF", "moduleName": "GIBMobileSdk", "declAttributes": ["Final", "AccessControl", "ObjC", "RawDocComment"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "getCapabilityName", "printedName": "getCapabilityName(_:)", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Capability", "printedName": "GIBMobileSdk.GIBSwiftManager.Capability", "usr": "s:12GIBMobileSdk15GIBSwiftManagerC10CapabilityO"}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBSwiftManager(im)getCapabilityName:", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC17getCapabilityNameySSSgAC0F0OF", "moduleName": "GIBMobileSdk", "declAttributes": ["Final", "AccessControl", "ObjC", "RawDocComment"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "requestIDFAPermission", "printedName": "requestIDFAPermission(completion:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(AppTrackingTransparency.ATTrackingManager.AuthorizationStatus) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "AuthorizationStatus", "printedName": "AppTrackingTransparency.ATTrackingManager.AuthorizationStatus", "usr": "c:@E@ATTrackingManagerAuthorizationStatus"}]}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBSwiftManager(im)requestIDFAPermissionWithCompletion:", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC21requestIDFAPermission10completionyySo010ATTrackingD19AuthorizationStatusVc_tF", "moduleName": "GIBMobileSdk", "intro_iOS": "14", "intro_tvOS": "14", "objc_name": "requestIDFAPermissionWithCompletion:", "declAttributes": ["Final", "AccessControl", "ObjC", "Available", "Available"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "getIDFAPermission", "printedName": "getIDFAPermission()", "children": [{"kind": "TypeNominal", "name": "AuthorizationStatus", "printedName": "AppTrackingTransparency.ATTrackingManager.AuthorizationStatus", "usr": "c:@E@ATTrackingManagerAuthorizationStatus"}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBSwiftManager(im)getIDFAPermission", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC17getIDFAPermissionSo010ATTrackingD19AuthorizationStatusVyF", "moduleName": "GIBMobileSdk", "intro_iOS": "14", "intro_tvOS": "14", "declAttributes": ["Final", "AccessControl", "ObjC", "Available", "Available"], "funcSelfKind": "NonMutating"}], "declKind": "Class", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBSwiftManager", "mangledName": "$s12GIBMobileSdk15GIBSwiftManagerC", "moduleName": "GIBMobileSdk", "declAttributes": ["AccessControl", "Final", "ObjC", "RawDocComment"], "superclassUsr": "c:objc(cs)NSObject", "hasMissingDesignatedInitializers": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "MachO", "printedName": "MachO", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "CommonCrypto", "printedName": "CommonCrypto", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "TypeDecl", "name": "FileIntegrityCheck", "printedName": "FileIntegrityCheck", "children": [{"kind": "Var", "name": "bundleID", "printedName": "bundleID", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.FileIntegrityCheck.Type) -> (Swift.String) -> GIBMobileSdk.FileIntegrityCheck", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(Swift.String) -> GIBMobileSdk.FileIntegrityCheck", "children": [{"kind": "TypeNominal", "name": "FileIntegrityCheck", "printedName": "GIBMobileSdk.FileIntegrityCheck", "usr": "s:12GIBMobileSdk18FileIntegrityCheckO"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}]}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.FileIntegrityCheck.Type", "children": [{"kind": "TypeNominal", "name": "FileIntegrityCheck", "printedName": "GIBMobileSdk.FileIntegrityCheck", "usr": "s:12GIBMobileSdk18FileIntegrityCheckO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk18FileIntegrityCheckO8bundleIDyACSScACmF", "mangledName": "$s12GIBMobileSdk18FileIntegrityCheckO8bundleIDyACSScACmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Var", "name": "mobileProvision", "printedName": "mobileProvision", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.FileIntegrityCheck.Type) -> (Swift.String) -> GIBMobileSdk.FileIntegrityCheck", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(Swift.String) -> GIBMobileSdk.FileIntegrityCheck", "children": [{"kind": "TypeNominal", "name": "FileIntegrityCheck", "printedName": "GIBMobileSdk.FileIntegrityCheck", "usr": "s:12GIBMobileSdk18FileIntegrityCheckO"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}]}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.FileIntegrityCheck.Type", "children": [{"kind": "TypeNominal", "name": "FileIntegrityCheck", "printedName": "GIBMobileSdk.FileIntegrityCheck", "usr": "s:12GIBMobileSdk18FileIntegrityCheckO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk18FileIntegrityCheckO15mobileProvisionyACSScACmF", "mangledName": "$s12GIBMobileSdk18FileIntegrityCheckO15mobileProvisionyACSScACmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Var", "name": "machO", "printedName": "machO", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.FileIntegrityCheck.Type) -> (Swift.String, Swift.String) -> GIBMobileSdk.FileIntegrityCheck", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(<PERSON>.String, Swift.String) -> GIBMobileSdk.FileIntegrityCheck", "children": [{"kind": "TypeNominal", "name": "FileIntegrityCheck", "printedName": "GIBMobileSdk.FileIntegrityCheck", "usr": "s:12GIBMobileSdk18FileIntegrityCheckO"}, {"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "(<PERSON><PERSON>, <PERSON>.String)", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}]}]}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.FileIntegrityCheck.Type", "children": [{"kind": "TypeNominal", "name": "FileIntegrityCheck", "printedName": "GIBMobileSdk.FileIntegrityCheck", "usr": "s:12GIBMobileSdk18FileIntegrityCheckO"}]}]}], "declKind": "EnumElement", "usr": "s:12GIBMobileSdk18FileIntegrityCheckO5machOyACSS_SStcACmF", "mangledName": "$s12GIBMobileSdk18FileIntegrityCheckO5machOyACSS_SStcACmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Var", "name": "description", "printedName": "description", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:12GIBMobileSdk18FileIntegrityCheckO11descriptionSSvp", "mangledName": "$s12GIBMobileSdk18FileIntegrityCheckO11descriptionSSvp", "moduleName": "GIBMobileSdk", "declAttributes": ["AccessControl"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:12GIBMobileSdk18FileIntegrityCheckO11descriptionSSvg", "mangledName": "$s12GIBMobileSdk18FileIntegrityCheckO11descriptionSSvg", "moduleName": "GIBMobileSdk", "isFromExtension": true, "accessorKind": "get"}]}], "declKind": "Enum", "usr": "s:12GIBMobileSdk18FileIntegrityCheckO", "mangledName": "$s12GIBMobileSdk18FileIntegrityCheckO", "moduleName": "GIBMobileSdk", "declAttributes": ["AccessControl", "RawDocComment"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "CoreLocation", "printedName": "CoreLocation", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "TypeDecl", "name": "LogType", "printedName": "LogType", "children": [{"kind": "Var", "name": "error", "printedName": "error", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.LogType.Type) -> GIBMobileSdk.LogType", "children": [{"kind": "TypeNominal", "name": "LogType", "printedName": "GIBMobileSdk.LogType", "usr": "c:@M@GIBMobileSdk@E@LogType"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.LogType.Type", "children": [{"kind": "TypeNominal", "name": "LogType", "printedName": "GIBMobileSdk.LogType", "usr": "c:@M@GIBMobileSdk@E@LogType"}]}]}], "declKind": "EnumElement", "usr": "c:@M@GIBMobileSdk@E@LogType@LogTypeError", "mangledName": "$s12GIBMobileSdk7LogTypeO5erroryA2CmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 0}, {"kind": "Var", "name": "warning", "printedName": "warning", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.LogType.Type) -> GIBMobileSdk.LogType", "children": [{"kind": "TypeNominal", "name": "LogType", "printedName": "GIBMobileSdk.LogType", "usr": "c:@M@GIBMobileSdk@E@LogType"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.LogType.Type", "children": [{"kind": "TypeNominal", "name": "LogType", "printedName": "GIBMobileSdk.LogType", "usr": "c:@M@GIBMobileSdk@E@LogType"}]}]}], "declKind": "EnumElement", "usr": "c:@M@GIBMobileSdk@E@LogType@LogTypeWarning", "mangledName": "$s12GIBMobileSdk7LogTypeO7warningyA2CmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 1}, {"kind": "Var", "name": "info", "printedName": "info", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.LogType.Type) -> GIBMobileSdk.LogType", "children": [{"kind": "TypeNominal", "name": "LogType", "printedName": "GIBMobileSdk.LogType", "usr": "c:@M@GIBMobileSdk@E@LogType"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.LogType.Type", "children": [{"kind": "TypeNominal", "name": "LogType", "printedName": "GIBMobileSdk.LogType", "usr": "c:@M@GIBMobileSdk@E@LogType"}]}]}], "declKind": "EnumElement", "usr": "c:@M@GIBMobileSdk@E@LogType@LogTypeInfo", "mangledName": "$s12GIBMobileSdk7LogTypeO4infoyA2CmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 2}, {"kind": "Var", "name": "verbose", "printedName": "verbose", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(GIBMobileSdk.LogType.Type) -> GIBMobileSdk.LogType", "children": [{"kind": "TypeNominal", "name": "LogType", "printedName": "GIBMobileSdk.LogType", "usr": "c:@M@GIBMobileSdk@E@LogType"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "GIBMobileSdk.LogType.Type", "children": [{"kind": "TypeNominal", "name": "LogType", "printedName": "GIBMobileSdk.LogType", "usr": "c:@M@GIBMobileSdk@E@LogType"}]}]}], "declKind": "EnumElement", "usr": "c:@M@GIBMobileSdk@E@LogType@LogTypeVerbose", "mangledName": "$s12GIBMobileSdk7LogTypeO7verboseyA2CmF", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment", "ObjC"], "fixedbinaryorder": 3}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(rawValue:)", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "GIBMobileSdk.LogType?", "children": [{"kind": "TypeNominal", "name": "LogType", "printedName": "GIBMobileSdk.LogType", "usr": "c:@M@GIBMobileSdk@E@LogType"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:12GIBMobileSdk7LogTypeO8rawValueACSgSi_tcfc", "mangledName": "$s12GIBMobileSdk7LogTypeO8rawValueACSgSi_tcfc", "moduleName": "GIBMobileSdk", "implicit": true, "init_kind": "Designated"}, {"kind": "Var", "name": "rawValue", "printedName": "rawValue", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "declKind": "Var", "usr": "s:12GIBMobileSdk7LogTypeO8rawValueSivp", "mangledName": "$s12GIBMobileSdk7LogTypeO8rawValueSivp", "moduleName": "GIBMobileSdk", "implicit": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "declKind": "Accessor", "usr": "s:12GIBMobileSdk7LogTypeO8rawValueSivg", "mangledName": "$s12GIBMobileSdk7LogTypeO8rawValueSivg", "moduleName": "GIBMobileSdk", "implicit": true, "accessorKind": "get"}]}], "declKind": "Enum", "usr": "c:@M@GIBMobileSdk@E@LogType", "mangledName": "$s12GIBMobileSdk7LogTypeO", "moduleName": "GIBMobileSdk", "declAttributes": ["AccessControl", "ObjC", "RawDocComment"], "enumRawTypeName": "Int", "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "RawRepresentable", "printedName": "RawRepresentable", "children": [{"kind": "TypeWitness", "name": "RawValue", "printedName": "RawValue", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}]}], "usr": "s:SY", "mangledName": "$sSY"}]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "TypeDecl", "name": "FPSecureView", "printedName": "FPSecureView", "children": [{"kind": "Var", "name": "secureContainer", "printedName": "secureContainer", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIView?", "children": [{"kind": "TypeNominal", "name": "UIView", "printedName": "UIKit.UIView", "usr": "c:objc(cs)UIView"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "c:@M@GIBMobileSdk@objc(cs)FPSecureView(py)secureContainer", "mangledName": "$s12GIBMobileSdk12FPSecureViewC15secureContainerSo6UIViewCSgvp", "moduleName": "GIBMobileSdk", "declAttributes": ["Final", "Preconcurrency", "Custom", "AccessControl", "ObjC"], "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "UIKit.UIView?", "children": [{"kind": "TypeNominal", "name": "UIView", "printedName": "UIKit.UIView", "usr": "c:objc(cs)UIView"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "c:@M@GIBMobileSdk@objc(cs)FPSecureView(im)secureContainer", "mangledName": "$s12GIBMobileSdk12FPSecureViewC15secureContainerSo6UIViewCSgvg", "moduleName": "GIBMobileSdk", "declAttributes": ["Final", "ObjC"], "accessorKind": "get"}]}, {"kind": "Var", "name": "isPreventScreenshot", "printedName": "isPreventScreenshot", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "declKind": "Var", "usr": "c:@M@GIBMobileSdk@objc(cs)FPSecureView(py)isPreventScreenshot", "mangledName": "$s12GIBMobileSdk12FPSecureViewC19isPreventScreenshotSbvp", "moduleName": "GIBMobileSdk", "declAttributes": ["Final", "Preconcurrency", "Custom", "AccessControl", "ObjC", "RawDocComment"], "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "declKind": "Accessor", "usr": "c:@M@GIBMobileSdk@objc(cs)FPSecureView(im)isPreventScreenshot", "mangledName": "$s12GIBMobileSdk12FPSecureViewC19isPreventScreenshotSbvg", "moduleName": "GIBMobileSdk", "declAttributes": ["Final", "ObjC"], "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "declKind": "Accessor", "usr": "c:@M@GIBMobileSdk@objc(cs)FPSecureView(im)setIsPreventScreenshot:", "mangledName": "$s12GIBMobileSdk12FPSecureViewC19isPreventScreenshotSbvs", "moduleName": "GIBMobileSdk", "declAttributes": ["Final", "ObjC"], "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:12GIBMobileSdk12FPSecureViewC19isPreventScreenshotSbvM", "mangledName": "$s12GIBMobileSdk12FPSecureViewC19isPreventScreenshotSbvM", "moduleName": "GIBMobileSdk", "implicit": true, "declAttributes": ["Final"], "accessorKind": "_modify"}]}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(frame:)", "children": [{"kind": "TypeNominal", "name": "FPSecureView", "printedName": "GIBMobileSdk.FPSecureView", "usr": "c:@M@GIBMobileSdk@objc(cs)FPSecureView"}, {"kind": "TypeNominal", "name": "CGRect", "printedName": "CoreFoundation.CGRect", "usr": "c:@S@CGRect"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "c:@M@GIBMobileSdk@objc(cs)FPSecureView(im)initWithFrame:", "mangledName": "$s12GIBMobileSdk12FPSecureViewC5frameACSo6CGRectV_tcfc", "moduleName": "GIBMobileSdk", "overriding": true, "objc_name": "initWithFrame:", "declAttributes": ["Dynamic", "ObjC", "Preconcurrency", "Custom", "Override", "AccessControl"], "init_kind": "Designated"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(coder:)", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "GIBMobileSdk.FPSecureView?", "children": [{"kind": "TypeNominal", "name": "FPSecureView", "printedName": "GIBMobileSdk.FPSecureView", "usr": "c:@M@GIBMobileSdk@objc(cs)FPSecureView"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "NSCoder", "printedName": "Foundation.NSCoder", "usr": "c:objc(cs)NSCoder"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "c:@M@GIBMobileSdk@objc(cs)FPSecureView(im)initWithCoder:", "mangledName": "$s12GIBMobileSdk12FPSecureViewC5coderACSgSo7NSCoderC_tcfc", "moduleName": "GIBMobileSdk", "overriding": true, "objc_name": "initWithCoder:", "declAttributes": ["Dynamic", "ObjC", "Preconcurrency", "Custom", "Required", "AccessControl"], "init_kind": "Designated"}], "declKind": "Class", "usr": "c:@M@GIBMobileSdk@objc(cs)FPSecureView", "mangledName": "$s12GIBMobileSdk12FPSecureViewC", "moduleName": "GIBMobileSdk", "declAttributes": ["Preconcurrency", "Custom", "Final", "AccessControl", "ObjC"], "superclassUsr": "c:objc(cs)UIView", "inheritsConvenienceInitializers": true, "superclassNames": ["UIKit.UIView", "UIKit.UIResponder", "ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}, {"kind": "Conformance", "name": "UITraitChangeObservable", "printedName": "UITraitChangeObservable", "usr": "s:5UIKit23UITraitChangeObservableP", "mangledName": "$s5UIKit23UITraitChangeObservableP"}, {"kind": "Conformance", "name": "__DefaultCustomPlaygroundQuickLookable", "printedName": "__DefaultCustomPlaygroundQuickLookable", "usr": "s:s38__DefaultCustomPlaygroundQuickLookableP", "mangledName": "$ss38__DefaultCustomPlaygroundQuickLookableP"}, {"kind": "Conformance", "name": "Sendable", "printedName": "Sendable", "usr": "s:s8SendableP", "mangledName": "$ss8SendableP"}]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "CoreLocation", "printedName": "CoreLocation", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "SystemConfiguration", "printedName": "SystemConfiguration", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "WebKit", "printedName": "WebKit", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "TypeDecl", "name": "GIBWebViewNavigationDelegate", "printedName": "GIBWebViewNavigationDelegate", "children": [{"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init()", "children": [{"kind": "TypeNominal", "name": "GIBWebViewNavigationDelegate", "printedName": "GIBMobileSdk.GIBWebViewNavigationDelegate", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBWebViewNavigationDelegate"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBWebViewNavigationDelegate(im)init", "mangledName": "$s12GIBMobileSdk28GIBWebViewNavigationDelegateCACycfc", "moduleName": "GIBMobileSdk", "overriding": true, "objc_name": "init", "declAttributes": ["Dynamic", "ObjC", "Preconcurrency", "Custom", "Override", "AccessControl", "RawDocComment"], "init_kind": "Designated"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(navigationDelegate:)", "children": [{"kind": "TypeNominal", "name": "GIBWebViewNavigationDelegate", "printedName": "GIBMobileSdk.GIBWebViewNavigationDelegate", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBWebViewNavigationDelegate"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "(any WebKit.WKNavigationDelegate)?", "children": [{"kind": "TypeNominal", "name": "WKNavigationDelegate", "printedName": "any WebKit.WKNavigationDelegate", "usr": "c:objc(pl)WKNavigationDelegate"}], "hasDefaultArg": true, "usr": "s:Sq"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBWebViewNavigationDelegate(im)initWithNavigationDelegate:", "mangledName": "$s12GIBMobileSdk28GIBWebViewNavigationDelegateC010navigationF0ACSo012WKNavigationF0_pSg_tcfc", "moduleName": "GIBMobileSdk", "objc_name": "initWithNavigationDelegate:", "declAttributes": ["Preconcurrency", "Custom", "AccessControl", "ObjC", "RawDocComment"], "init_kind": "Designated"}, {"kind": "Function", "name": "webView", "printedName": "webView(_:didFinish:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "WKWebView", "printedName": "WebKit.WKWebView", "usr": "c:objc(cs)WKWebView"}, {"kind": "TypeNominal", "name": "ImplicitlyUnwrappedOptional", "printedName": "WebKit.WKNavigation?", "children": [{"kind": "TypeNominal", "name": "WKNavigation", "printedName": "WebKit.WKNavigation", "usr": "c:objc(cs)WKNavigation"}], "usr": "s:Sq"}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBWebViewNavigationDelegate(im)webView:didFinishNavigation:", "mangledName": "$s12GIBMobileSdk28GIBWebViewNavigationDelegateC03webD0_9didFinishySo05WKWebD0C_So12WKNavigationCSgtF", "moduleName": "GIBMobileSdk", "objc_name": "webView:didFinishNavigation:", "declAttributes": ["ObjC", "Preconcurrency", "Custom", "AccessControl", "Final"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "webView", "printedName": "webView(_:decidePolicyFor:decisionHandler:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "WKWebView", "printedName": "WebKit.WKWebView", "usr": "c:objc(cs)WKWebView"}, {"kind": "TypeNominal", "name": "WKNavigationAction", "printedName": "WebKit.WKNavigationAction", "usr": "c:objc(cs)WKNavigationAction"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(WebKit.WKNavigationActionPolicy) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "WKNavigationActionPolicy", "printedName": "WebKit.WKNavigationActionPolicy", "usr": "c:@E@WKNavigationActionPolicy"}]}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBWebViewNavigationDelegate(im)webView:decidePolicyForNavigationAction:decisionHandler:", "mangledName": "$s12GIBMobileSdk28GIBWebViewNavigationDelegateC03webD0_15decidePolicyFor15decisionHandlerySo05WKWebD0C_So18WKNavigationActionCySo0noI0VctF", "moduleName": "GIBMobileSdk", "objc_name": "webView:decidePolicyForNavigationAction:decisionHandler:", "declAttributes": ["ObjC", "Preconcurrency", "Custom", "AccessControl", "Final"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "webView", "printedName": "webView(_:decidePolicyFor:preferences:decisionHandler:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "WKWebView", "printedName": "WebKit.WKWebView", "usr": "c:objc(cs)WKWebView"}, {"kind": "TypeNominal", "name": "WKNavigationAction", "printedName": "WebKit.WKNavigationAction", "usr": "c:objc(cs)WKNavigationAction"}, {"kind": "TypeNominal", "name": "WKWebpagePreferences", "printedName": "WebKit.WKWebpagePreferences", "usr": "c:objc(cs)WKWebpagePreferences"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(WebKit.WKNavigationActionPolicy, WebKit.WKWebpagePreferences) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "(WebKit.WKNavigationActionPolicy, WebKit.WKWebpagePreferences)", "children": [{"kind": "TypeNominal", "name": "WKNavigationActionPolicy", "printedName": "WebKit.WKNavigationActionPolicy", "usr": "c:@E@WKNavigationActionPolicy"}, {"kind": "TypeNominal", "name": "WKWebpagePreferences", "printedName": "WebKit.WKWebpagePreferences", "usr": "c:objc(cs)WKWebpagePreferences"}]}]}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBWebViewNavigationDelegate(im)webView:decidePolicyForNavigationAction:preferences:decisionHandler:", "mangledName": "$s12GIBMobileSdk28GIBWebViewNavigationDelegateC03webD0_15decidePolicyFor11preferences15decisionHandlerySo05WKWebD0C_So18WKNavigationActionCSo20WKWebpagePreferencesCySo0opI0V_AMtctF", "moduleName": "GIBMobileSdk", "intro_iOS": "13.0", "objc_name": "webView:decidePolicyForNavigationAction:preferences:decision<PERSON><PERSON><PERSON>:", "declAttributes": ["ObjC", "Preconcurrency", "Custom", "AccessControl", "Final", "Available"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "webView", "printedName": "webView(_:decidePolicyFor:decisionHandler:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "WKWebView", "printedName": "WebKit.WKWebView", "usr": "c:objc(cs)WKWebView"}, {"kind": "TypeNominal", "name": "WKNavigationResponse", "printedName": "WebKit.WKNavigationResponse", "usr": "c:objc(cs)WKNavigationResponse"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(WebKit.WKNavigationResponsePolicy) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "WKNavigationResponsePolicy", "printedName": "WebKit.WKNavigationResponsePolicy", "usr": "c:@E@WKNavigationResponsePolicy"}]}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBWebViewNavigationDelegate(im)webView:decidePolicyForNavigationResponse:decisionHandler:", "mangledName": "$s12GIBMobileSdk28GIBWebViewNavigationDelegateC03webD0_15decidePolicyFor15decisionHandlerySo05WKWebD0C_So20WKNavigationResponseCySo0noI0VctF", "moduleName": "GIBMobileSdk", "objc_name": "webView:decidePolicyForNavigationResponse:decisionHandler:", "declAttributes": ["ObjC", "Preconcurrency", "Custom", "AccessControl", "Final"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "webView", "printedName": "webView(_:didStartProvisionalNavigation:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "WKWebView", "printedName": "WebKit.WKWebView", "usr": "c:objc(cs)WKWebView"}, {"kind": "TypeNominal", "name": "ImplicitlyUnwrappedOptional", "printedName": "WebKit.WKNavigation?", "children": [{"kind": "TypeNominal", "name": "WKNavigation", "printedName": "WebKit.WKNavigation", "usr": "c:objc(cs)WKNavigation"}], "usr": "s:Sq"}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBWebViewNavigationDelegate(im)webView:didStartProvisionalNavigation:", "mangledName": "$s12GIBMobileSdk28GIBWebViewNavigationDelegateC03webD0_019didStartProvisionalE0ySo05WKWebD0C_So12WKNavigationCSgtF", "moduleName": "GIBMobileSdk", "objc_name": "webView:didStartProvisionalNavigation:", "declAttributes": ["ObjC", "Preconcurrency", "Custom", "AccessControl", "Final"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "webView", "printedName": "webView(_:didReceiveServerRedirectForProvisionalNavigation:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "WKWebView", "printedName": "WebKit.WKWebView", "usr": "c:objc(cs)WKWebView"}, {"kind": "TypeNominal", "name": "ImplicitlyUnwrappedOptional", "printedName": "WebKit.WKNavigation?", "children": [{"kind": "TypeNominal", "name": "WKNavigation", "printedName": "WebKit.WKNavigation", "usr": "c:objc(cs)WKNavigation"}], "usr": "s:Sq"}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBWebViewNavigationDelegate(im)webView:didReceiveServerRedirectForProvisionalNavigation:", "mangledName": "$s12GIBMobileSdk28GIBWebViewNavigationDelegateC03webD0_038didReceiveServerRedirectForProvisionalE0ySo05WKWebD0C_So12WKNavigationCSgtF", "moduleName": "GIBMobileSdk", "objc_name": "webView:didReceiveServerRedirectForProvisionalNavigation:", "declAttributes": ["ObjC", "Preconcurrency", "Custom", "AccessControl", "Final"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "webView", "printedName": "webView(_:didFailProvisionalNavigation:withError:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "WKWebView", "printedName": "WebKit.WKWebView", "usr": "c:objc(cs)WKWebView"}, {"kind": "TypeNominal", "name": "ImplicitlyUnwrappedOptional", "printedName": "WebKit.WKNavigation?", "children": [{"kind": "TypeNominal", "name": "WKNavigation", "printedName": "WebKit.WKNavigation", "usr": "c:objc(cs)WKNavigation"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Error", "printedName": "any Swift.Error", "usr": "s:s5ErrorP"}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBWebViewNavigationDelegate(im)webView:didFailProvisionalNavigation:withError:", "mangledName": "$s12GIBMobileSdk28GIBWebViewNavigationDelegateC03webD0_018didFailProvisionalE09withErrorySo05WKWebD0C_So12WKNavigationCSgs0L0_ptF", "moduleName": "GIBMobileSdk", "objc_name": "webView:didFailProvisionalNavigation:withError:", "declAttributes": ["ObjC", "Preconcurrency", "Custom", "AccessControl", "Final"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "webView", "printedName": "webView(_:didCommit:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "WKWebView", "printedName": "WebKit.WKWebView", "usr": "c:objc(cs)WKWebView"}, {"kind": "TypeNominal", "name": "ImplicitlyUnwrappedOptional", "printedName": "WebKit.WKNavigation?", "children": [{"kind": "TypeNominal", "name": "WKNavigation", "printedName": "WebKit.WKNavigation", "usr": "c:objc(cs)WKNavigation"}], "usr": "s:Sq"}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBWebViewNavigationDelegate(im)webView:didCommitNavigation:", "mangledName": "$s12GIBMobileSdk28GIBWebViewNavigationDelegateC03webD0_9didCommitySo05WKWebD0C_So12WKNavigationCSgtF", "moduleName": "GIBMobileSdk", "objc_name": "webView:didCommitNavigation:", "declAttributes": ["ObjC", "Preconcurrency", "Custom", "AccessControl", "Final"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "webView", "printedName": "webView(_:didFail:withError:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "WKWebView", "printedName": "WebKit.WKWebView", "usr": "c:objc(cs)WKWebView"}, {"kind": "TypeNominal", "name": "ImplicitlyUnwrappedOptional", "printedName": "WebKit.WKNavigation?", "children": [{"kind": "TypeNominal", "name": "WKNavigation", "printedName": "WebKit.WKNavigation", "usr": "c:objc(cs)WKNavigation"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Error", "printedName": "any Swift.Error", "usr": "s:s5ErrorP"}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBWebViewNavigationDelegate(im)webView:didFailNavigation:withError:", "mangledName": "$s12GIBMobileSdk28GIBWebViewNavigationDelegateC03webD0_7didFail9withErrorySo05WKWebD0C_So12WKNavigationCSgs0K0_ptF", "moduleName": "GIBMobileSdk", "objc_name": "webView:didFailNavigation:withError:", "declAttributes": ["ObjC", "Preconcurrency", "Custom", "AccessControl", "Final"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "webView", "printedName": "webView(_:didReceive:completionHandler:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "WKWebView", "printedName": "WebKit.WKWebView", "usr": "c:objc(cs)WKWebView"}, {"kind": "TypeNominal", "name": "URLAuthenticationChallenge", "printedName": "Foundation.URLAuthenticationChallenge", "usr": "c:objc(cs)NSURLAuthenticationChallenge"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(Foundation.URLSession.AuthChallengeDisposition, Foundation.URLCredential?) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "(Foundation.URLSession.AuthChallengeDisposition, Foundation.URLCredential?)", "children": [{"kind": "TypeNominal", "name": "AuthChallengeDisposition", "printedName": "Foundation.URLSession.AuthChallengeDisposition", "usr": "c:@E@NSURLSessionAuthChallengeDisposition"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Foundation.URLCredential?", "children": [{"kind": "TypeNominal", "name": "URLCredential", "printedName": "Foundation.URLCredential", "usr": "c:objc(cs)NSURLCredential"}], "usr": "s:Sq"}]}]}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBWebViewNavigationDelegate(im)webView:didReceiveAuthenticationChallenge:completionHandler:", "mangledName": "$s12GIBMobileSdk28GIBWebViewNavigationDelegateC03webD0_10didReceive17completionHandlerySo05WKWebD0C_So28NSURLAuthenticationChallengeCySo016NSURLSessionAuthN11DispositionV_So15NSURLCredentialCSgtctF", "moduleName": "GIBMobileSdk", "objc_name": "webView:didReceiveAuthenticationChallenge:completionHandler:", "declAttributes": ["ObjC", "Preconcurrency", "Custom", "AccessControl", "Final"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "webViewWebContentProcessDidTerminate", "printedName": "webViewWebContentProcessDidTerminate(_:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "WKWebView", "printedName": "WebKit.WKWebView", "usr": "c:objc(cs)WKWebView"}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBWebViewNavigationDelegate(im)webViewWebContentProcessDidTerminate:", "mangledName": "$s12GIBMobileSdk28GIBWebViewNavigationDelegateC03webD29WebContentProcessDidTerminateyySo05WKWebD0CF", "moduleName": "GIBMobileSdk", "objc_name": "webViewWebContentProcessDidTerminate:", "declAttributes": ["ObjC", "Preconcurrency", "Custom", "AccessControl", "Final"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "webView", "printedName": "webView(_:authenticationChallenge:shouldAllowDeprecatedTLS:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "WKWebView", "printedName": "WebKit.WKWebView", "usr": "c:objc(cs)WKWebView"}, {"kind": "TypeNominal", "name": "URLAuthenticationChallenge", "printedName": "Foundation.URLAuthenticationChallenge", "usr": "c:objc(cs)NSURLAuthenticationChallenge"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(<PERSON><PERSON>) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}]}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBWebViewNavigationDelegate(im)webView:authenticationChallenge:shouldAllowDeprecatedTLS:", "mangledName": "$s12GIBMobileSdk28GIBWebViewNavigationDelegateC03webD0_23authenticationChallenge24shouldAllowDeprecatedTLSySo05WKWebD0C_So019NSURLAuthenticationI0CySbctF", "moduleName": "GIBMobileSdk", "intro_iOS": "14.0", "objc_name": "webView:authenticationChallenge:shouldAllowDeprecatedTLS:", "declAttributes": ["ObjC", "Preconcurrency", "Custom", "AccessControl", "Final", "Available"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "webView", "printedName": "webView(_:navigationAction:didBecome:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "WKWebView", "printedName": "WebKit.WKWebView", "usr": "c:objc(cs)WKWebView"}, {"kind": "TypeNominal", "name": "WKNavigationAction", "printedName": "WebKit.WKNavigationAction", "usr": "c:objc(cs)WKNavigationAction"}, {"kind": "TypeNominal", "name": "WKDownload", "printedName": "WebKit.WKDownload", "usr": "c:objc(cs)WKDownload"}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBWebViewNavigationDelegate(im)webView:navigationAction:didBecomeDownload:", "mangledName": "$s12GIBMobileSdk28GIBWebViewNavigationDelegateC03webD0_16navigationAction9didBecomeySo05WKWebD0C_So012WKNavigationI0CSo10WKDownloadCtF", "moduleName": "GIBMobileSdk", "intro_iOS": "14.5", "objc_name": "webView:navigationAction:didBecomeDownload:", "declAttributes": ["ObjC", "Preconcurrency", "Custom", "AccessControl", "Final", "Available"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "webView", "printedName": "webView(_:navigationResponse:didBecome:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "WKWebView", "printedName": "WebKit.WKWebView", "usr": "c:objc(cs)WKWebView"}, {"kind": "TypeNominal", "name": "WKNavigationResponse", "printedName": "WebKit.WKNavigationResponse", "usr": "c:objc(cs)WKNavigationResponse"}, {"kind": "TypeNominal", "name": "WKDownload", "printedName": "WebKit.WKDownload", "usr": "c:objc(cs)WKDownload"}], "declKind": "Func", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBWebViewNavigationDelegate(im)webView:navigationResponse:didBecomeDownload:", "mangledName": "$s12GIBMobileSdk28GIBWebViewNavigationDelegateC03webD0_18navigationResponse9didBecomeySo05WKWebD0C_So012WKNavigationI0CSo10WKDownloadCtF", "moduleName": "GIBMobileSdk", "intro_iOS": "14.5", "objc_name": "webView:navigationResponse:didBecomeDownload:", "declAttributes": ["ObjC", "Preconcurrency", "Custom", "AccessControl", "Final", "Available"], "funcSelfKind": "NonMutating"}], "declKind": "Class", "usr": "c:@M@GIBMobileSdk@objc(cs)GIBWebViewNavigationDelegate", "mangledName": "$s12GIBMobileSdk28GIBWebViewNavigationDelegateC", "moduleName": "GIBMobileSdk", "declAttributes": ["Preconcurrency", "Custom", "Final", "AccessControl", "ObjC", "RawDocComment"], "superclassUsr": "c:objc(cs)NSObject", "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}, {"kind": "Conformance", "name": "Sendable", "printedName": "Sendable", "usr": "s:s8SendableP", "mangledName": "$ss8SendableP"}]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "CoreMotion", "printedName": "CoreMotion", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "MachO", "printedName": "MachO", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "CommonCrypto", "printedName": "CommonCrypto", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "CoreLocation", "printedName": "CoreLocation", "declKind": "Import", "moduleName": "GIBMobileSdk"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "GIBMobileSdk", "declAttributes": ["RawDocComment"]}, {"kind": "TypeDecl", "name": "UIWindow", "printedName": "UIWindow", "children": [{"kind": "Function", "name": "gibPreventScreenshots", "printedName": "gibPreventScreenshots()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Func", "usr": "c:@CM@GIBMobileSdk@@objc(cs)UIWindow(im)gibPreventScreenshots", "mangledName": "$sSo8UIWindowC12GIBMobileSdkE21gibPreventScreenshotsyyF", "moduleName": "GIBMobileSdk", "declAttributes": ["Dynamic", "Preconcurrency", "Custom", "ObjC", "RawDocComment"], "isFromExtension": true, "funcSelfKind": "NonMutating"}], "declKind": "Class", "usr": "c:objc(cs)UIWindow", "moduleName": "UIKit", "isOpen": true, "intro_iOS": "2.0", "objc_name": "UIWindow", "declAttributes": ["Preconcurrency", "Available", "ObjC", "NonSendable", "Custom", "Dynamic"], "superclassUsr": "c:objc(cs)UIView", "isExternal": true, "inheritsConvenienceInitializers": true, "superclassNames": ["UIKit.UIView", "UIKit.UIResponder", "ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}, {"kind": "Conformance", "name": "UITraitChangeObservable", "printedName": "UITraitChangeObservable", "usr": "s:5UIKit23UITraitChangeObservableP", "mangledName": "$s5UIKit23UITraitChangeObservableP"}, {"kind": "Conformance", "name": "__DefaultCustomPlaygroundQuickLookable", "printedName": "__DefaultCustomPlaygroundQuickLookable", "usr": "s:s38__DefaultCustomPlaygroundQuickLookableP", "mangledName": "$ss38__DefaultCustomPlaygroundQuickLookableP"}]}, {"kind": "TypeDecl", "name": "UIView", "printedName": "UIView", "children": [{"kind": "Function", "name": "fpPinLayout", "printedName": "fpPinLayout(_:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Attribute", "printedName": "UIKit.NSLayoutConstraint.Attribute", "usr": "c:@E@NSLayoutAttribute"}], "declKind": "Func", "usr": "c:@CM@GIBMobileSdk@@objc(cs)UIView(im)fpPinLayout:", "mangledName": "$sSo6UIViewC12GIBMobileSdkE11fpPinLayoutyySo17NSLayoutAttributeVF", "moduleName": "GIBMobileSdk", "declAttributes": ["Dynamic", "Preconcurrency", "Custom", "ObjC"], "isFromExtension": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "fpPinLayoutEdges", "printedName": "fpPinLayoutEdges()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Func", "usr": "c:@CM@GIBMobileSdk@@objc(cs)UIView(im)fpPinLayoutEdges", "mangledName": "$sSo6UIViewC12GIBMobileSdkE16fpPinLayoutEdgesyyF", "moduleName": "GIBMobileSdk", "declAttributes": ["Dynamic", "Preconcurrency", "Custom", "ObjC"], "isFromExtension": true, "funcSelfKind": "NonMutating"}], "declKind": "Class", "usr": "c:objc(cs)UIView", "moduleName": "UIKit", "isOpen": true, "intro_iOS": "2.0", "objc_name": "UIView", "declAttributes": ["Preconcurrency", "Available", "ObjC", "NonSendable", "Custom", "Dynamic"], "superclassUsr": "c:objc(cs)UIResponder", "isExternal": true, "inheritsConvenienceInitializers": true, "superclassNames": ["UIKit.UIResponder", "ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}, {"kind": "Conformance", "name": "UITraitChangeObservable", "printedName": "UITraitChangeObservable", "usr": "s:5UIKit23UITraitChangeObservableP", "mangledName": "$s5UIKit23UITraitChangeObservableP"}, {"kind": "Conformance", "name": "__DefaultCustomPlaygroundQuickLookable", "printedName": "__DefaultCustomPlaygroundQuickLookable", "usr": "s:s38__DefaultCustomPlaygroundQuickLookableP", "mangledName": "$ss38__DefaultCustomPlaygroundQuickLookableP"}]}], "json_format_version": 8}, "ConstValues": [{"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/GlobalId/BootTimeManager.swift", "kind": "StringLiteral", "offset": 3418, "length": 14, "value": "\"fp_saved_now\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/GlobalId/BootTimeManager.swift", "kind": "StringLiteral", "offset": 3469, "length": 16, "value": "\"fp_save_uptime\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Location/LocationManager.swift", "kind": "StringLiteral", "offset": 286, "length": 26, "value": "\"sendLocationNotification\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Location/LocationManager.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 667, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBHelperManager.swift", "kind": "IntegerLiteral", "offset": 280, "length": 4, "value": "1000"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBCapability.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2611, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Cellular/CellularCapability.swift", "kind": "IntegerLiteral", "offset": 369, "length": 1, "value": "5"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Cellular/CellularCapability.swift", "kind": "StringLiteral", "offset": 412, "length": 18, "value": "\"0000000100000001\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Cellular/CellularCapability.swift", "kind": "Array", "offset": 732, "length": 2, "value": "[]"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Cellular/CellularCapability.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 781, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBPacket.swift", "kind": "StringLiteral", "offset": 253, "length": 7, "value": "\"1.0.0\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBPacket.swift", "kind": "StringLiteral", "offset": 283, "length": 7, "value": "\"1.1.0\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBPacket.swift", "kind": "StringLiteral", "offset": 313, "length": 7, "value": "\"2.0.0\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBPacket.swift", "kind": "StringLiteral", "offset": 343, "length": 7, "value": "\"2.1.0\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBPacket.swift", "kind": "StringLiteral", "offset": 373, "length": 7, "value": "\"3.0.0\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBPacket.swift", "kind": "StringLiteral", "offset": 403, "length": 7, "value": "\"3.1.0\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBPacket.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2620, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Security/SecurityManager/DebuggerChecker.swift", "kind": "IntegerLiteral", "offset": 906, "length": 2, "value": "-2"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Security/SecurityManager/DebuggerChecker.swift", "kind": "StringLiteral", "offset": 957, "length": 8, "value": "\"ptrace\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Security/SecurityManager/DebuggerChecker.swift", "kind": "IntegerLiteral", "offset": 1178, "length": 2, "value": "31"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Security/SecurityManager/DebuggerChecker.swift", "kind": "IntegerLiteral", "offset": 1182, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Security/SecurityManager/DebuggerChecker.swift", "kind": "IntegerLiteral", "offset": 1185, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Security/SecurityManager/DebuggerChecker.swift", "kind": "IntegerLiteral", "offset": 1188, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Security/SecurityManager/DebuggerChecker.swift", "kind": "IntegerLiteral", "offset": 1216, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Security/SecurityManager/DebuggerChecker.swift", "kind": "StringLiteral", "offset": 1238, "length": 75, "value": "\"Error occured when calling ptrace(). Denying debugger may not be reliable\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/build.swift", "kind": "IntegerLiteral", "offset": 52, "length": 10, "value": "1748046444"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionSensorObserver.swift", "kind": "IntegerLiteral", "offset": 417, "length": 1, "value": "5"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionSensorObserver.swift", "kind": "IntegerLiteral", "offset": 466, "length": 1, "value": "5"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionSensorObserver.swift", "kind": "IntegerLiteral", "offset": 1058, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionSensorObserver.swift", "kind": "IntegerLiteral", "offset": 1369, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionSensorObserver.swift", "kind": "IntegerLiteral", "offset": 1411, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionSensorObserver.swift", "kind": "IntegerLiteral", "offset": 1445, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionSensorObserver.swift", "kind": "IntegerLiteral", "offset": 1484, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionSensorObserver.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1522, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionSensorObserver.swift", "kind": "Array", "offset": 1577, "length": 2, "value": "[]"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Web/WebCapability.swift", "kind": "Array", "offset": 516, "length": 2, "value": "[]"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/System/SystemCapability.swift", "kind": "StringLiteral", "offset": 404, "length": 9, "value": "\"device1\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Behavior/BehaviorManager.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 275, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Managers/GibIdManager.swift", "kind": "StringLiteral", "offset": 261, "length": 29, "value": "\"com.group_ib.MobileSdk.uuid\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Security/SecurityManager/JailbreakChecker.swift", "kind": "IntegerLiteral", "offset": 36789, "length": 2, "value": "-2"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Security/SecurityManager/JailbreakChecker.swift", "kind": "StringLiteral", "offset": 36836, "length": 6, "value": "\"fork\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Security/SecurityManager/JailbreakChecker.swift", "kind": "IntegerLiteral", "offset": 37027, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Security/SecurityManager/JailbreakChecker.swift", "kind": "IntegerLiteral", "offset": 37059, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionObserver.swift", "kind": "Dictionary", "offset": 792, "length": 3, "value": "[]"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionObserver.swift", "kind": "IntegerLiteral", "offset": 835, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionObserver.swift", "kind": "IntegerLiteral", "offset": 883, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionObserver.swift", "kind": "Array", "offset": 945, "length": 2, "value": "[]"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionObserver.swift", "kind": "IntegerLiteral", "offset": 1053, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/CloudIdentifier/CloudIdentifierCapability.swift", "kind": "StringLiteral", "offset": 292, "length": 24, "value": "\"__GIB__user_identifier\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/CloudIdentifier/CloudIdentifierCapability.swift", "kind": "IntegerLiteral", "offset": 362, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/CloudIdentifier/CloudIdentifierCapability.swift", "kind": "IntegerLiteral", "offset": 400, "length": 1, "value": "3"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/CloudIdentifier/CloudIdentifierCapability.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 850, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionCapability.swift", "kind": "StringLiteral", "offset": 288, "length": 19, "value": "\"getNewMotionValue\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionCapability.swift", "kind": "StringLiteral", "offset": 365, "length": 22, "value": "\"stopMotionCapability\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionCapability.swift", "kind": "FloatLiteral", "offset": 538, "length": 5, "value": "0.050"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionCapability.swift", "kind": "FloatLiteral", "offset": 654, "length": 4, "value": "0.01"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionCapability.swift", "kind": "IntegerLiteral", "offset": 715, "length": 1, "value": "5"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionCapability.swift", "kind": "IntegerLiteral", "offset": 773, "length": 4, "value": "8000"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionCapability.swift", "kind": "Dictionary", "offset": 1210, "length": 3, "value": "[]"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionCapability.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1250, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionCapability.swift", "kind": "Dictionary", "offset": 1368, "length": 3, "value": "[]"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Capture/CaptureCapability.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2069, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Network/NetworkCapability.swift", "kind": "StringLiteral", "offset": 331, "length": 5, "value": "\"en0\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Network/NetworkCapability.swift", "kind": "StringLiteral", "offset": 356, "length": 5, "value": "\"en1\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Network/NetworkCapability.swift", "kind": "StringLiteral", "offset": 386, "length": 9, "value": "\"pdp_ip0\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Network/NetworkCapability.swift", "kind": "StringLiteral", "offset": 421, "length": 9, "value": "\"pdp_ip1\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Network/NetworkCapability.swift", "kind": "IntegerLiteral", "offset": 521, "length": 2, "value": "30"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/PortScan/PortScanCapability.swift", "kind": "IntegerLiteral", "offset": 465, "length": 2, "value": "-1"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/PortScan/PortScanCapability.swift", "kind": "IntegerLiteral", "offset": 3516, "length": 4, "value": "1081"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/PortScan/PortScanCapability.swift", "kind": "StringLiteral", "offset": 3552, "length": 11, "value": "\"127.0.0.1\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/GlobalId/GlobalIdCapability.swift", "kind": "IntegerLiteral", "offset": 1665, "length": 2, "value": "10"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Behavior/Models/BehaviorActivityModel.swift", "kind": "Array", "offset": 803, "length": 2, "value": "[]"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Behavior/Models/BehaviorActivityModel.swift", "kind": "Array", "offset": 1183, "length": 2, "value": "[]"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Managers/KeychainManager.swift", "kind": "StringLiteral", "offset": 1980, "length": 27, "value": "\"com.group-ib.GIBMobileSDK\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Location/LocationStorage.swift", "kind": "StringLiteral", "offset": 612, "length": 41, "value": "\"com.group-ib.GIBMobileSDK.locationQueue\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Location/LocationStorage.swift", "kind": "IntegerLiteral", "offset": 2012, "length": 1, "value": "5"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Location/LocationStorage.swift", "kind": "IntegerLiteral", "offset": 2055, "length": 3, "value": "500"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftManager.swift", "kind": "IntegerLiteral", "offset": 1489, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftManager.swift", "kind": "IntegerLiteral", "offset": 1858, "length": 1, "value": "1"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftManager.swift", "kind": "IntegerLiteral", "offset": 2558, "length": 1, "value": "2"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftManager.swift", "kind": "IntegerLiteral", "offset": 3178, "length": 1, "value": "3"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftManager.swift", "kind": "IntegerLiteral", "offset": 4128, "length": 1, "value": "4"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftManager.swift", "kind": "IntegerLiteral", "offset": 4921, "length": 1, "value": "5"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftManager.swift", "kind": "IntegerLiteral", "offset": 5280, "length": 1, "value": "6"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftManager.swift", "kind": "IntegerLiteral", "offset": 5788, "length": 1, "value": "7"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftManager.swift", "kind": "IntegerLiteral", "offset": 6394, "length": 1, "value": "8"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftManager.swift", "kind": "IntegerLiteral", "offset": 6731, "length": 1, "value": "9"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftManager.swift", "kind": "IntegerLiteral", "offset": 7375, "length": 2, "value": "10"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftManager.swift", "kind": "IntegerLiteral", "offset": 7736, "length": 2, "value": "11"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftManager.swift", "kind": "IntegerLiteral", "offset": 8172, "length": 2, "value": "12"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftManager.swift", "kind": "IntegerLiteral", "offset": 8728, "length": 2, "value": "13"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftManager.swift", "kind": "IntegerLiteral", "offset": 9146, "length": 2, "value": "14"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftManager.swift", "kind": "IntegerLiteral", "offset": 9419, "length": 2, "value": "15"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftManager.swift", "kind": "IntegerLiteral", "offset": 9995, "length": 2, "value": "16"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftManager.swift", "kind": "IntegerLiteral", "offset": 10224, "length": 2, "value": "17"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftManager.swift", "kind": "IntegerLiteral", "offset": 10349, "length": 2, "value": "18"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftManager.swift", "kind": "IntegerLiteral", "offset": 10603, "length": 2, "value": "19"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftManager.swift", "kind": "IntegerLiteral", "offset": 10631, "length": 2, "value": "20"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftManager.swift", "kind": "IntegerLiteral", "offset": 10667, "length": 2, "value": "21"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Behavior/Models/BehaviorE.swift", "kind": "Array", "offset": 1640, "length": 2, "value": "[]"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBLogManager.swift", "kind": "IntegerLiteral", "offset": 311, "length": 7, "value": "1"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBLogManager.swift", "kind": "IntegerLiteral", "offset": 349, "length": 4, "value": "2"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBLogManager.swift", "kind": "IntegerLiteral", "offset": 408, "length": 7, "value": "3"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBConstants.swift", "kind": "StringLiteral", "offset": 216, "length": 20, "value": "\"__GIB__cloud_calls\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBConstants.swift", "kind": "StringLiteral", "offset": 273, "length": 41, "value": "\"-[MobileSDKService setGIBSwiftListener]\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBConstants.swift", "kind": "StringLiteral", "offset": 404, "length": 36, "value": "\"request_iOS_SDK.send_by_js_request\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBConstants.swift", "kind": "StringLiteral", "offset": 493, "length": 28, "value": "\".callNotificationExtension\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBConstants.swift", "kind": "StringLiteral", "offset": 569, "length": 20, "value": "\"group.callNotifier\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBConstants.swift", "kind": "StringLiteral", "offset": 618, "length": 12, "value": "\"phones.gib\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/PreventScreenshot/FPSecureView.swift", "kind": "StringLiteral", "offset": 1664, "length": 12, "value": "\"CanvasView\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/PreventScreenshot/FPSecureView.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1738, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/PreventScreenshot/FPSecureView.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1785, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/PreventScreenshot/FPSecureView.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1866, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftReachability.swift", "kind": "StringLiteral", "offset": 1689, "length": 26, "value": "\"swiftReachabilityChanged\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftReachability.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2914, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftReachability.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 3971, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/GIBSwiftReachability.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 4082, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Extensions/UIDevice.swift", "kind": "StringLiteral", "offset": 383, "length": 28, "value": "\"SIMULATOR_MODEL_IDENTIFIER\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/System/Modules/InterteamModule.swift", "kind": "StringLiteral", "offset": 283, "length": 42, "value": "\"com.group_ib.MobileSdk.gib_ids_array_key\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/System/Modules/InterteamModule.swift", "kind": "StringLiteral", "offset": 367, "length": 41, "value": "\"com.group_ib.mobile_sdk.shared_keychain\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/System/Modules/InterteamModule.swift", "kind": "StringLiteral", "offset": 443, "length": 14, "value": "\"bundleSeedID\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionSensor.swift", "kind": "IntegerLiteral", "offset": 334, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionSensor.swift", "kind": "IntegerLiteral", "offset": 353, "length": 1, "value": "1"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionSensor.swift", "kind": "IntegerLiteral", "offset": 372, "length": 1, "value": "2"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionSensor.swift", "kind": "IntegerLiteral", "offset": 396, "length": 1, "value": "3"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Proxy/ProxyCapability.swift", "kind": "IntegerLiteral", "offset": 294, "length": 2, "value": "10"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Proxy/ProxyCapability.swift", "kind": "StringLiteral", "offset": 325, "length": 8, "value": "\"scopes\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Proxy/ProxyCapability.swift", "kind": "StringLiteral", "offset": 366, "length": 12, "value": "\"__SCOPED__\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Swizzle/Swizzle Extensions/SwizzleUIViewController.swift", "kind": "StringLiteral", "offset": 391, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Behavior/Models/BehaviorSensorModel.swift", "kind": "Array", "offset": 462, "length": 2, "value": "[]"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Behavior/Models/BehaviorSensorModel.swift", "kind": "IntegerLiteral", "offset": 506, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Behavior/Models/BehaviorSensorModel.swift", "kind": "IntegerLiteral", "offset": 542, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Behavior/BehaviorCapability.swift", "kind": "IntegerLiteral", "offset": 422, "length": 1, "value": "1"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Behavior/BehaviorCapability.swift", "kind": "IntegerLiteral", "offset": 479, "length": 2, "value": "10"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Behavior/BehaviorCapability.swift", "kind": "IntegerLiteral", "offset": 527, "length": 1, "value": "1"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Behavior/BehaviorCapability.swift", "kind": "StringLiteral", "offset": 584, "length": 36, "value": "\"UICompatibilityInputViewController\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Behavior/BehaviorCapability.swift", "kind": "StringLiteral", "offset": 665, "length": 32, "value": "\"UISystemKeyboardDockController\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Behavior/BehaviorCapability.swift", "kind": "Array", "offset": 1067, "length": 2, "value": "[]"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Behavior/BehaviorCapability.swift", "kind": "Array", "offset": 1231, "length": 2, "value": "[]"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Behavior/BehaviorCapability.swift", "kind": "Array", "offset": 1445, "length": 2, "value": "[]"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Security/SecurityManager/ProxyChecker.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 266, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Swizzle/SwizzleCapability.swift", "kind": "StringLiteral", "offset": 648, "length": 45, "value": "\"com.group-ib.GIBMobileSDK.SwizzleCapability\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Swizzle/SwizzleCapability.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1064, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Location/LocationStatus.swift", "kind": "StringLiteral", "offset": 245, "length": 15, "value": "\"notDetermined\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Location/LocationStatus.swift", "kind": "StringLiteral", "offset": 283, "length": 12, "value": "\"restricted\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Location/LocationStatus.swift", "kind": "StringLiteral", "offset": 314, "length": 8, "value": "\"denied\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Location/LocationStatus.swift", "kind": "StringLiteral", "offset": 351, "length": 18, "value": "\"authorizedAlways\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Location/LocationStatus.swift", "kind": "StringLiteral", "offset": 401, "length": 21, "value": "\"authorizedWhenInUse\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Location/LocationStatus.swift", "kind": "StringLiteral", "offset": 442, "length": 9, "value": "\"unknown\""}, {"filePath": "/Users/<USER>/XcodeProjects/sdk/builds/VDhvWnX9/0/sb/ios/sdk/GIBMobileSdk/Swift parts/Capabilities/Motion/MotionFullObserver.swift", "kind": "Array", "offset": 521, "length": 2, "value": "[]"}]}