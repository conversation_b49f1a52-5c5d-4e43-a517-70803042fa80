// swift-interface-format-version: 1.0
// swift-compiler-version: Apple Swift version 6.0.3 effective-5.10 (swiftlang-6.0.3.1.10 clang-1600.0.30.1)
// swift-module-flags: -target x86_64-apple-ios15.6-simulator -enable-objc-interop -enable-library-evolution -swift-version 5 -enforce-exclusivity=checked -Osize -enable-bare-slash-regex -module-name GIBMobileSdk
// swift-module-flags-ignorable: -no-verify-emitted-module-interface
import AVFoundation
import AdSupport
import AppTrackingTransparency
import SystemConfiguration.CaptiveNetwork
import CommonCrypto
import CoreLocation
import CoreMotion
import CoreTelephony
import Darwin
import DeviceCheck
import ExternalAccessory
import Foundation
@_exported import GIBMobileSdk
import GameController
import LocalAuthentication
import MachO
import Network
import ObjectiveC
import Security
import Swift
import SystemConfiguration
import UIKit
import WebKit
import _Concurrency
import _StringProcessing
import _SwiftConcurrencyShims
import MachO.arch
import os
extension UIKit.UIWindow {
  @objc @_Concurrency.MainActor @preconcurrency dynamic public func gibPreventScreenshots()
}
@_hasMissingDesignatedInitializers final public class GIBHelperManager {
  public static var getTimeOfDay: Swift.Double {
    get
  }
  @objc deinit
}
open class GIBCapability {
  open class var shared: GIBMobileSdk.GIBCapability {
    get
  }
  public var isRun: Swift.Bool {
    get
  }
  public init()
  open func run() throws
  open func stop() throws
  @objc deinit
}
final public class GIBPacket {
  public enum Versions : Swift.String {
    case v1_0_0
    case v1_1_0
    case v2_0_0
    case v2_1_0
    case v3_0_0
    case v3_1_0
    public init?(rawValue: Swift.String)
    public typealias RawValue = Swift.String
    public var rawValue: Swift.String {
      get
    }
  }
  public enum Providers {
    case advertise
    case cellular
    case network
    case location
    case cloud
    case behavior
    case auth
    case capture
    case deviceStatus
    case audio
    case battery
    case proxy
    case keyboard
    case security
    case portScan
    case globalId
    case custom(Swift.String)
  }
  final public var packetDictionary: Swift.Dictionary<Swift.String, Any> {
    get
  }
  public init(version: GIBMobileSdk.GIBPacket.Versions, provider: GIBMobileSdk.GIBPacket.Providers, send: Swift.Bool = true, data: Any)
  @objc deinit
}
public protocol GIBModelProtocol : Swift.Hashable {
  var dictionary: [Swift.String : Any] { get }
}
@_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers @objc final public class BehaviorManager : ObjectiveC.NSObject {
  @objc public static var isExtendedData: Swift.Bool
  @objc deinit
}
@_hasMissingDesignatedInitializers final public class GIBApplication {
  public static var shared: UIKit.UIApplication? {
    get
  }
  @objc deinit
}
@_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers final public class DeviceStatusCapability : GIBMobileSdk.GIBCapability {
  override final public class var shared: GIBMobileSdk.DeviceStatusCapability {
    get
  }
  @objc deinit
  override final public func stop() throws
  final public func enableProximityMonitoring(_ isEnable: Swift.Bool)
}
@objc public protocol GIBSwiftManagerDelegate {
  @objc func sendDictionaryFromSwift(dictionary: Foundation.NSDictionary, isRepeatData: Swift.Bool)
  @objc func sendLog(_ log: Swift.String, type: GIBMobileSdk.LogType)
  @objc func sendInitAppWebView(_ webView: WebKit.WKWebView)
}
@_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers @objc final public class GIBSwiftManager : ObjectiveC.NSObject {
  @objc public enum Capability : Swift.Int, Swift.CaseIterable {
    case batteryStatus = 0
    case cellular = 1
    case call = 2
    case passcode = 3
    case webView = 4
    case network = 5
    case motion = 6
    case swizzle = 7
    case location = 8
    case audio = 9
    case cloudIdentifier = 10
    case deviceStatus = 11
    case capture = 12
    case apps = 13
    case proxy = 14
    case keyboard = 15
    case behavior = 16
    case preventScreenshots = 17
    case security = 18
    case advertise = 19
    case portScan = 20
    case globalId = 21
    public init?(rawValue: Swift.Int)
    public typealias AllCases = [GIBMobileSdk.GIBSwiftManager.Capability]
    public typealias RawValue = Swift.Int
    nonisolated public static var allCases: [GIBMobileSdk.GIBSwiftManager.Capability] {
      get
    }
    public var rawValue: Swift.Int {
      get
    }
  }
  @objc public static let shared: GIBMobileSdk.GIBSwiftManager
  weak final public var delegate: (any GIBMobileSdk.GIBSwiftManagerDelegate)? {
    get
  }
  @discardableResult
  @objc final public func enableCapability(_ capability: GIBMobileSdk.GIBSwiftManager.Capability) -> Swift.Bool
  @discardableResult
  @objc final public func disableCapability(_ capability: GIBMobileSdk.GIBSwiftManager.Capability) -> Swift.Bool
  @objc final public func run()
  @objc final public func stop()
  @objc final public func isRun(_ capability: GIBMobileSdk.GIBSwiftManager.Capability) -> Swift.Bool
  @objc final public func allStatus() -> Swift.Dictionary<Swift.String, Swift.Bool>
  @objc final public func setOutput(_ delegate: any GIBMobileSdk.GIBSwiftManagerDelegate)
  @objc final public func getCapabilityName(_ capability: GIBMobileSdk.GIBSwiftManager.Capability) -> Swift.String?
  @available(iOS 14, tvOS 14, *)
  @objc final public func requestIDFAPermission(completion: @escaping (AppTrackingTransparency.ATTrackingManager.AuthorizationStatus) -> Swift.Void)
  @available(iOS 14, tvOS 14, *)
  @objc final public func getIDFAPermission() -> AppTrackingTransparency.ATTrackingManager.AuthorizationStatus
  @objc deinit
}
public enum FileIntegrityCheck {
  case bundleID(Swift.String)
  case mobileProvision(Swift.String)
  case machO(Swift.String, Swift.String)
}
extension GIBMobileSdk.FileIntegrityCheck {
  public var description: Swift.String {
    get
  }
}
public typealias FileIntegrityCheckResult = (result: Swift.Bool, hitChecks: [GIBMobileSdk.FileIntegrityCheck])
@objc public enum LogType : Swift.Int {
  case error
  case warning
  case info
  case verbose
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@_inheritsConvenienceInitializers @objc @_Concurrency.MainActor @preconcurrency final public class FPSecureView : UIKit.UIView {
  @objc @_Concurrency.MainActor @preconcurrency final public var secureContainer: UIKit.UIView? {
    @objc get
  }
  @objc @_Concurrency.MainActor @preconcurrency final public var isPreventScreenshot: Swift.Bool {
    @objc get
    @objc set
  }
  @_Concurrency.MainActor @preconcurrency @objc override dynamic public init(frame: CoreFoundation.CGRect)
  @_Concurrency.MainActor @preconcurrency @objc required dynamic public init?(coder: Foundation.NSCoder)
  @objc deinit
}
@objc extension UIKit.UIView {
  @objc @_Concurrency.MainActor @preconcurrency dynamic public func fpPinLayout(_ type: UIKit.NSLayoutConstraint.Attribute)
  @objc @_Concurrency.MainActor @preconcurrency dynamic public func fpPinLayoutEdges()
}
@_inheritsConvenienceInitializers @objc @_Concurrency.MainActor @preconcurrency final public class GIBWebViewNavigationDelegate : ObjectiveC.NSObject, WebKit.WKNavigationDelegate {
  @_Concurrency.MainActor @preconcurrency @objc override dynamic public init()
  @objc @_Concurrency.MainActor @preconcurrency public init(navigationDelegate: (any WebKit.WKNavigationDelegate)? = nil)
  @_Concurrency.MainActor @preconcurrency @objc final public func webView(_ webView: WebKit.WKWebView, didFinish navigation: WebKit.WKNavigation!)
  @_Concurrency.MainActor @preconcurrency @objc final public func webView(_ webView: WebKit.WKWebView, decidePolicyFor navigationAction: WebKit.WKNavigationAction, decisionHandler: @escaping (WebKit.WKNavigationActionPolicy) -> Swift.Void)
  @available(iOS 13.0, *)
  @_Concurrency.MainActor @preconcurrency @objc final public func webView(_ webView: WebKit.WKWebView, decidePolicyFor navigationAction: WebKit.WKNavigationAction, preferences: WebKit.WKWebpagePreferences, decisionHandler: @escaping (WebKit.WKNavigationActionPolicy, WebKit.WKWebpagePreferences) -> Swift.Void)
  @_Concurrency.MainActor @preconcurrency @objc final public func webView(_ webView: WebKit.WKWebView, decidePolicyFor navigationResponse: WebKit.WKNavigationResponse, decisionHandler: @escaping (WebKit.WKNavigationResponsePolicy) -> Swift.Void)
  @_Concurrency.MainActor @preconcurrency @objc final public func webView(_ webView: WebKit.WKWebView, didStartProvisionalNavigation navigation: WebKit.WKNavigation!)
  @_Concurrency.MainActor @preconcurrency @objc final public func webView(_ webView: WebKit.WKWebView, didReceiveServerRedirectForProvisionalNavigation navigation: WebKit.WKNavigation!)
  @_Concurrency.MainActor @preconcurrency @objc final public func webView(_ webView: WebKit.WKWebView, didFailProvisionalNavigation navigation: WebKit.WKNavigation!, withError error: any Swift.Error)
  @_Concurrency.MainActor @preconcurrency @objc final public func webView(_ webView: WebKit.WKWebView, didCommit navigation: WebKit.WKNavigation!)
  @_Concurrency.MainActor @preconcurrency @objc final public func webView(_ webView: WebKit.WKWebView, didFail navigation: WebKit.WKNavigation!, withError error: any Swift.Error)
  @_Concurrency.MainActor @preconcurrency @objc final public func webView(_ webView: WebKit.WKWebView, didReceive challenge: Foundation.URLAuthenticationChallenge, completionHandler: @escaping (Foundation.URLSession.AuthChallengeDisposition, Foundation.URLCredential?) -> Swift.Void)
  @_Concurrency.MainActor @preconcurrency @objc final public func webViewWebContentProcessDidTerminate(_ webView: WebKit.WKWebView)
  @available(iOS 14.0, *)
  @_Concurrency.MainActor @preconcurrency @objc final public func webView(_ webView: WebKit.WKWebView, authenticationChallenge challenge: Foundation.URLAuthenticationChallenge, shouldAllowDeprecatedTLS decisionHandler: @escaping (Swift.Bool) -> Swift.Void)
  @available(iOS 14.5, *)
  @_Concurrency.MainActor @preconcurrency @objc final public func webView(_ webView: WebKit.WKWebView, navigationAction: WebKit.WKNavigationAction, didBecome download: WebKit.WKDownload)
  @available(iOS 14.5, *)
  @_Concurrency.MainActor @preconcurrency @objc final public func webView(_ webView: WebKit.WKWebView, navigationResponse: WebKit.WKNavigationResponse, didBecome download: WebKit.WKDownload)
  @objc deinit
}
extension GIBMobileSdk.GIBPacket.Versions : Swift.Equatable {}
extension GIBMobileSdk.GIBPacket.Versions : Swift.Hashable {}
extension GIBMobileSdk.GIBPacket.Versions : Swift.RawRepresentable {}
extension GIBMobileSdk.GIBSwiftManager.Capability : Swift.Equatable {}
extension GIBMobileSdk.GIBSwiftManager.Capability : Swift.Hashable {}
extension GIBMobileSdk.GIBSwiftManager.Capability : Swift.RawRepresentable {}
extension GIBMobileSdk.LogType : Swift.Equatable {}
extension GIBMobileSdk.LogType : Swift.Hashable {}
extension GIBMobileSdk.LogType : Swift.RawRepresentable {}
extension GIBMobileSdk.GIBWebViewNavigationDelegate : Swift.Sendable {}
