{"name": "group-ib-fp", "version": "2.3.0", "description": "React Native module with Fraud Protection", "main": "lib/commonjs/index", "module": "lib/module/index", "types": "lib/typescript/index.d.ts", "react-native": "src/index", "source": "src/index", "files": ["src", "lib", "android", "ios", "cpp", "*.podsp<PERSON>", "!lib/typescript/example", "!ios/build", "!android/build", "!android/gradle", "!android/gradlew", "!android/gradlew.bat", "!android/local.properties", "!**/__tests__", "!**/__fixtures__", "!**/__mocks__", "!**/.*"], "scripts": {"test": "jest", "typescript": "tsc --noEmit", "lint": "eslint \"**/*.{js,ts,tsx}\"", "release": "release-it", "example": "yarn --cwd example", "clean": "del-cli android/build example/android/build example/android/app/build example/ios/build", "bootstrap": "yarn example && yarn install && yarn example pods"}, "keywords": ["react-native", "ios", "android"], "author": "Group-IB <<EMAIL>> (https://group-ib.com)", "homepage": "https://group-ib.com", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "resolutions": {"@types/react": "17.0.21"}, "peerDependencies": {"react": "*", "react-native": "*"}, "jest": {"preset": "react-native", "modulePathIgnorePatterns": ["<rootDir>/example/node_modules", "<rootDir>/lib/"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular"}}}, "eslintConfig": {"root": true, "extends": ["@react-native-community", "prettier"], "rules": {"prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}, "eslintIgnore": ["node_modules/", "lib/"], "prettier": {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}}