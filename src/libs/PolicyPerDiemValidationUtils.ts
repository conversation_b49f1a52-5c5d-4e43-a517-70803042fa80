import type {FormInputErrors, FormOnyxValues} from '@components/Form/types';
import CONST from '@src/CONST';
import ONYXKEYS from '@src/ONYXKEYS';
import type {CustomUnit, Rate} from '@src/types/onyx/Policy';
import type {Transaction} from '@src/types/onyx';
import {convertToBackendAmount} from './CurrencyUtils';
import getPermittedDecimalSeparator from './getPermittedDecimalSeparator';
import {translateLocal} from './Localize';
import {replaceAllDigits} from './MoneyRequestUtils';
import {parseFloatAnyLocale} from './NumberUtils';
import {isValidDate} from './ValidationUtils';

type PerDiemForm = FormOnyxValues<typeof ONYXKEYS.FORMS.WORKSPACE_PER_DIEM_FORM>;
type PerDiemFormErrors = FormInputErrors<typeof ONYXKEYS.FORMS.WORKSPACE_PER_DIEM_FORM>;

/**
 * Validates per diem destination name
 */
function validateDestination(
    destination: string,
    customUnit: CustomUnit | undefined,
    currentDestination?: string,
): string | undefined {
    const destinationTrimmed = destination.trim();

    if (!destinationTrimmed) {
        return translateLocal('common.error.fieldRequired');
    }

    if (destinationTrimmed.length > CONST.MAX_LENGTH_256) {
        return translateLocal('common.error.characterLimitExceedCounter', {
            length: destinationTrimmed.length,
            limit: CONST.MAX_LENGTH_256,
        });
    }

    // Check for duplicate destination names (excluding current destination when editing)
    const existingDestinations = Object.values(customUnit?.rates ?? {});
    const isDuplicate = existingDestinations.some(
        (rate) => rate.name?.toLowerCase() === destinationTrimmed.toLowerCase() && rate.name !== currentDestination,
    );

    if (isDuplicate) {
        return translateLocal('workspace.perDiem.errors.existingDestinationError', {destination: destinationTrimmed});
    }

    return undefined;
}

/**
 * Validates per diem subrate name
 */
function validateSubrate(
    subrate: string,
    selectedRate: Rate | undefined,
    currentSubrateId?: string,
): string | undefined {
    const subrateTrimmed = subrate.trim();

    if (!subrateTrimmed) {
        return translateLocal('common.error.fieldRequired');
    }

    if (subrateTrimmed.length > CONST.MAX_LENGTH_256) {
        return translateLocal('common.error.characterLimitExceedCounter', {
            length: subrateTrimmed.length,
            limit: CONST.MAX_LENGTH_256,
        });
    }

    // Check for duplicate subrate names within the same destination (excluding current subrate when editing)
    const existingSubrates = selectedRate?.subRates ?? [];
    const isDuplicate = existingSubrates.some(
        (existingSubrate) => 
            existingSubrate.name?.toLowerCase() === subrateTrimmed.toLowerCase() && 
            existingSubrate.id !== currentSubrateId,
    );

    if (isDuplicate) {
        return translateLocal('workspace.perDiem.errors.existingSubrateError', {subrate: subrateTrimmed});
    }

    return undefined;
}

/**
 * Validates per diem amount
 */
function validateAmount(
    amount: string,
    toLocaleDigit: (arg: string) => string,
    currency = CONST.CURRENCY.USD,
): string | undefined {
    const amountTrimmed = amount.trim();

    if (!amountTrimmed || amountTrimmed === '-') {
        return translateLocal('common.error.fieldRequired');
    }

    const parsedAmount = replaceAllDigits(amountTrimmed, toLocaleDigit);
    const decimalSeparator = toLocaleDigit('.');
    const permittedDecimalSeparator = getPermittedDecimalSeparator(decimalSeparator);

    // Validate amount format (allow negative values for per diem)
    const amountRegex = RegExp(
        String.raw`^-?\d{0,8}([${permittedDecimalSeparator}]\d{0,${CONST.MAX_TAX_RATE_DECIMAL_PLACES}})?$`,
        'i',
    );

    if (!amountRegex.test(parsedAmount)) {
        return translateLocal('common.error.invalidAmount');
    }

    const numericAmount = parseFloatAnyLocale(parsedAmount);
    const backendAmount = convertToBackendAmount(numericAmount);

    // Check if amount is zero
    if (backendAmount === 0) {
        return translateLocal('common.error.fieldRequired');
    }

    return undefined;
}

/**
 * Validates per diem rate value (similar to distance rate validation)
 */
function validatePerDiemRateValue(
    values: PerDiemForm,
    customUnit: CustomUnit | undefined,
    toLocaleDigit: (arg: string) => string,
    currentRateValue?: number,
): PerDiemFormErrors {
    const errors: PerDiemFormErrors = {};

    if (values.amount) {
        const amountError = validateAmount(values.amount, toLocaleDigit);
        if (amountError) {
            errors.amount = amountError;
        } else {
            // Additional validation for duplicate rates
            const parsedRate = replaceAllDigits(values.amount, toLocaleDigit);
            const convertedRate = parseFloatAnyLocale(parsedRate) * CONST.POLICY.CUSTOM_UNIT_RATE_BASE_OFFSET;
            
            const ratesList = Object.values(customUnit?.rates ?? {})
                .filter((rate) => currentRateValue !== rate.rate)
                .flatMap((rate) => rate.subRates ?? [])
                .map((subRate) => parseFloat(Number(subRate.rate || 0).toFixed(10)));

            if (ratesList.some((r) => r === convertedRate)) {
                errors.amount = translateLocal('workspace.perDiem.errors.existingRateError', {rate: Number(values.amount)});
            }
        }
    }

    return errors;
}

/**
 * Validates per diem date range
 */
function validateDateRange(startDate: string, endDate: string): {startDate?: string; endDate?: string} {
    const errors: {startDate?: string; endDate?: string} = {};

    if (!startDate) {
        errors.startDate = translateLocal('common.error.fieldRequired');
    } else if (!isValidDate(startDate)) {
        errors.startDate = translateLocal('common.error.dateInvalid');
    }

    if (!endDate) {
        errors.endDate = translateLocal('common.error.fieldRequired');
    } else if (!isValidDate(endDate)) {
        errors.endDate = translateLocal('common.error.dateInvalid');
    }

    // Validate that end date is not before start date
    if (startDate && endDate && isValidDate(startDate) && isValidDate(endDate)) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        
        if (end < start) {
            errors.endDate = translateLocal('workspace.perDiem.errors.endDateBeforeStartDate');
        }
    }

    return errors;
}

/**
 * Validates per diem transaction eligibility
 */
function validatePerDiemTransaction(
    transaction: Transaction,
    customUnit: CustomUnit | undefined,
): string | undefined {
    // Check if transaction has required per diem structure
    if (!transaction.comment?.customUnit) {
        return translateLocal('workspace.perDiem.errors.missingCustomUnit');
    }

    const transactionCustomUnit = transaction.comment.customUnit;

    // Validate custom unit ID matches
    if (transactionCustomUnit.customUnitID !== customUnit?.customUnitID) {
        return translateLocal('workspace.perDiem.errors.invalidCustomUnitID');
    }

    // Validate custom unit name
    if (transactionCustomUnit.name !== CONST.CUSTOM_UNITS.NAME_PER_DIEM_INTERNATIONAL) {
        return translateLocal('workspace.perDiem.errors.invalidCustomUnitName');
    }

    // Validate that transaction has subRates
    if (!transactionCustomUnit.subRates || transactionCustomUnit.subRates.length === 0) {
        return translateLocal('workspace.perDiem.errors.missingSubRates');
    }

    // Validate that customUnitRateID exists in policy rates
    const rateID = transactionCustomUnit.customUnitRateID;
    if (!rateID || !customUnit?.rates?.[rateID]) {
        return translateLocal('workspace.perDiem.errors.invalidRateID');
    }

    // Validate date range
    const dates = transactionCustomUnit.attributes?.dates;
    if (!dates || !dates.start || !dates.end) {
        return translateLocal('workspace.perDiem.errors.missingDateRange');
    }

    const dateErrors = validateDateRange(dates.start, dates.end);
    if (dateErrors.startDate || dateErrors.endDate) {
        return translateLocal('workspace.perDiem.errors.invalidDateRange');
    }

    return undefined;
}

/**
 * Validates complete per diem form
 */
function validatePerDiemForm(
    values: PerDiemForm,
    customUnit: CustomUnit | undefined,
    toLocaleDigit: (arg: string) => string,
    selectedRate?: Rate,
    currentSubrateId?: string,
): PerDiemFormErrors {
    const errors: PerDiemFormErrors = {};

    // Validate destination
    if (values.destination) {
        const destinationError = validateDestination(values.destination, customUnit);
        if (destinationError) {
            errors.destination = destinationError;
        }
    }

    // Validate subrate
    if (values.subrate) {
        const subrateError = validateSubrate(values.subrate, selectedRate, currentSubrateId);
        if (subrateError) {
            errors.subrate = subrateError;
        }
    }

    // Validate amount
    if (values.amount) {
        const amountError = validateAmount(values.amount, toLocaleDigit);
        if (amountError) {
            errors.amount = amountError;
        }
    }

    return errors;
}

export {
    validateDestination,
    validateSubrate,
    validateAmount,
    validatePerDiemRateValue,
    validateDateRange,
    validatePerDiemTransaction,
    validatePerDiemForm,
};
