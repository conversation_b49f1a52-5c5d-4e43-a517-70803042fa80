import type {OnyxEntry} from 'react-native-onyx';
import CONST from '@src/CONST';
import type {Policy, CustomUnit} from '@src/types/onyx/Policy';
import type {Transaction} from '@src/types/onyx/Transaction';
import {getPerDiemCustomUnit} from './PolicyUtils';
import {isPerDiemRequest} from './TransactionUtils';

/**
 * Validates if a transaction is eligible for per diem based on customUnitID matching
 */
function isEligiblePerDiemTransaction(
    transaction: OnyxEntry<Transaction>,
    customUnitID: string,
): boolean {
    if (!transaction || !customUnitID) {
        return false;
    }

    // Check if it's a per diem transaction
    if (!isPerDiemRequest(transaction)) {
        return false;
    }

    // Check if customUnitID matches
    return transaction?.comment?.customUnit?.customUnitID === customUnitID;
}

/**
 * Validates if a transaction is eligible for a specific per diem rate
 */
function isEligiblePerDiemRateTransaction(
    transaction: OnyxEntry<Transaction>,
    customUnitID: string,
    rateID: string,
): boolean {
    if (!isEligiblePerDiemTransaction(transaction, customUnitID)) {
        return false;
    }

    // Check if customUnitRateID matches the specific rate
    return transaction?.comment?.customUnit?.customUnitRateID === rateID;
}

/**
 * Validates if a transaction is eligible for a specific per diem subrate
 */
function isEligiblePerDiemSubRateTransaction(
    transaction: OnyxEntry<Transaction>,
    customUnitID: string,
    rateID: string,
    subRateID: string,
): boolean {
    if (!isEligiblePerDiemRateTransaction(transaction, customUnitID, rateID)) {
        return false;
    }

    // Check if transaction has subRates and contains the specific subRateID
    const subRates = transaction?.comment?.customUnit?.subRates;
    return subRates?.some(subRate => subRate.id === subRateID) ?? false;
}

/**
 * Gets eligible transaction IDs for per diem policy
 */
function getEligiblePerDiemTransactionIDs(
    transactions: Record<string, Transaction>,
    policy: OnyxEntry<Policy>,
): Set<string> {
    const customUnit = getPerDiemCustomUnit(policy);
    
    if (!customUnit?.customUnitID) {
        return new Set();
    }

    return Object.values(transactions).reduce((transactionIDs, transaction) => {
        if (isEligiblePerDiemTransaction(transaction, customUnit.customUnitID)) {
            transactionIDs.add(transaction.transactionID);
        }
        return transactionIDs;
    }, new Set<string>());
}

/**
 * Gets eligible transaction IDs for a specific per diem rate
 */
function getEligiblePerDiemRateTransactionIDs(
    transactions: Record<string, Transaction>,
    customUnitID: string,
    rateID: string,
): Set<string> {
    if (!customUnitID || !rateID) {
        return new Set();
    }

    return Object.values(transactions).reduce((transactionIDs, transaction) => {
        if (isEligiblePerDiemRateTransaction(transaction, customUnitID, rateID)) {
            transactionIDs.add(transaction.transactionID);
        }
        return transactionIDs;
    }, new Set<string>());
}

/**
 * Validates per diem custom unit configuration
 */
function validatePerDiemCustomUnit(customUnit: OnyxEntry<CustomUnit>): boolean {
    if (!customUnit) {
        return false;
    }

    // Check if it's a per diem custom unit
    if (customUnit.name !== CONST.CUSTOM_UNITS.NAME_PER_DIEM_INTERNATIONAL) {
        return false;
    }

    // Check if it has a valid customUnitID
    if (!customUnit.customUnitID) {
        return false;
    }

    // Check if it's enabled
    return customUnit.enabled === true;
}

export {
    isEligiblePerDiemTransaction,
    isEligiblePerDiemRateTransaction,
    isEligiblePerDiemSubRateTransaction,
    getEligiblePerDiemTransactionIDs,
    getEligiblePerDiemRateTransactionIDs,
    validatePerDiemCustomUnit,
};
