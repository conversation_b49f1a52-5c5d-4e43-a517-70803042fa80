'worklet'

class peg$SyntaxError{}
// @generated by Peggy 4.0.3.
//
// https://peggyjs.org/


// CAUTION: DO NOT DIRECTLY ALTER OR MODIFY `searchParser.js` OR `autocompleteParser.js`
// These files are auto-generated by <PERSON> from grammar files (*.peggy). 
// To make changes, edit the corresponding *.peggy files only.
// Use the `generate-search-parser` and `generate-autocomplete-parser` scripts to regenerate parsers after modifications.

function peg$subclass(child, parent) {
  function C() { this.constructor = child; }
  C.prototype = parent.prototype;
  child.prototype = new C();
}

function temporary(message, expected, found, location) {
  var self = Error.call(this, message);
  // istanbul ignore next Check is a necessary evil to support older environments
  if (Object.setPrototypeOf) {
    Object.setPrototypeOf(self, peg$SyntaxError.prototype);
  }
  self.expected = expected;
  self.found = found;
  self.location = location;
  self.name = "SyntaxError";
  return self;
}



function peg$padEnd(str, targetLength, padString) {
  padString = padString || " ";
  if (str.length > targetLength) { return str; }
  targetLength -= str.length;
  padString += padString.repeat(targetLength);
  return str + padString.slice(0, targetLength);
}

peg$SyntaxError.prototype.format = function(sources) {
  var str = "Error: " + this.message;
  if (this.location) {
    var src = null;
    var k;
    for (k = 0; k < sources.length; k++) {
      if (sources[k].source === this.location.source) {
        src = sources[k].text.split(/\r\n|\n|\r/g);
        break;
      }
    }
    var s = this.location.start;
    var offset_s = (this.location.source && (typeof this.location.source.offset === "function"))
      ? this.location.source.offset(s)
      : s;
    var loc = this.location.source + ":" + offset_s.line + ":" + offset_s.column;
    if (src) {
      var e = this.location.end;
      var filler = peg$padEnd("", offset_s.line.toString().length, ' ');
      var line = src[s.line - 1];
      var last = s.line === e.line ? e.column : line.length + 1;
      var hatLen = (last - s.column) || 1;
      str += "\n --> " + loc + "\n"
          + filler + " |\n"
          + offset_s.line + " | " + line + "\n"
          + filler + " | " + peg$padEnd("", s.column - 1, ' ')
          + peg$padEnd("", hatLen, "^");
    } else {
      str += "\n at " + loc;
    }
  }
  return str;
};

peg$SyntaxError.buildMessage = function(expected, found) {
  var DESCRIBE_EXPECTATION_FNS = {
    literal: function(expectation) {
      return "\"" + literalEscape(expectation.text) + "\"";
    },

    class: function(expectation) {
      var escapedParts = expectation.parts.map(function(part) {
        return Array.isArray(part)
          ? classEscape(part[0]) + "-" + classEscape(part[1])
          : classEscape(part);
      });

      return "[" + (expectation.inverted ? "^" : "") + escapedParts.join("") + "]";
    },

    any: function() {
      return "any character";
    },

    end: function() {
      return "end of input";
    },

    other: function(expectation) {
      return expectation.description;
    }
  };

  function hex(ch) {
    return ch.charCodeAt(0).toString(16).toUpperCase();
  }

  function literalEscape(s) {
    return s
      .replace(/\\/g, "\\\\")
      .replace(/"/g,  "\\\"")
      .replace(/\0/g, "\\0")
      .replace(/\t/g, "\\t")
      .replace(/\n/g, "\\n")
      .replace(/\r/g, "\\r")
      .replace(/[\x00-\x0F]/g,          function(ch) { return "\\x0" + hex(ch); })
      .replace(/[\x10-\x1F\x7F-\x9F]/g, function(ch) { return "\\x"  + hex(ch); });
  }

  function classEscape(s) {
    return s
      .replace(/\\/g, "\\\\")
      .replace(/\]/g, "\\]")
      .replace(/\^/g, "\\^")
      .replace(/-/g,  "\\-")
      .replace(/\0/g, "\\0")
      .replace(/\t/g, "\\t")
      .replace(/\n/g, "\\n")
      .replace(/\r/g, "\\r")
      .replace(/[\x00-\x0F]/g,          function(ch) { return "\\x0" + hex(ch); })
      .replace(/[\x10-\x1F\x7F-\x9F]/g, function(ch) { return "\\x"  + hex(ch); });
  }

  function describeExpectation(expectation) {
    return DESCRIBE_EXPECTATION_FNS[expectation.type](expectation);
  }

  function describeExpected(expected) {
    var descriptions = expected.map(describeExpectation);
    var i, j;

    descriptions.sort();

    if (descriptions.length > 0) {
      for (i = 1, j = 1; i < descriptions.length; i++) {
        if (descriptions[i - 1] !== descriptions[i]) {
          descriptions[j] = descriptions[i];
          j++;
        }
      }
      descriptions.length = j;
    }

    switch (descriptions.length) {
      case 1:
        return descriptions[0];

      case 2:
        return descriptions[0] + " or " + descriptions[1];

      default:
        return descriptions.slice(0, -1).join(", ")
          + ", or "
          + descriptions[descriptions.length - 1];
    }
  }

  function describeFound(found) {
    return found ? "\"" + literalEscape(found) + "\"" : "end of input";
  }

  return "Expected " + describeExpected(expected) + " but " + describeFound(found) + " found.";
};

function peg$parse(input, options) {
  options = options !== undefined ? options : {};

  var peg$FAILED = {};
  var peg$source = options.grammarSource;

  var peg$startRuleFunctions = { query: peg$parsequery };
  var peg$startRuleFunction = peg$parsequery;

  var peg$c0 = ",";
  var peg$c1 = "date";
  var peg$c2 = "amount";
  var peg$c3 = "merchant";
  var peg$c4 = "description";
  var peg$c5 = "reportid";
  var peg$c6 = "report-id";
  var peg$c7 = "keyword";
  var peg$c8 = "in";
  var peg$c9 = "currency";
  var peg$c10 = "groupCurrency";
  var peg$c11 = "group-currency";
  var peg$c12 = "tag";
  var peg$c13 = "category";
  var peg$c14 = "to";
  var peg$c15 = "exporter";
  var peg$c16 = "payer";
  var peg$c17 = "taxrate";
  var peg$c18 = "tax-rate";
  var peg$c19 = "cardID";
  var peg$c20 = "card";
  var peg$c21 = "from";
  var peg$c22 = "expenseType";
  var peg$c23 = "expense-type";
  var peg$c24 = "withdrawalType";
  var peg$c25 = "withdrawal-type";
  var peg$c26 = "billable";
  var peg$c27 = "reimbursable";
  var peg$c28 = "type";
  var peg$c29 = "status";
  var peg$c30 = "sortBy";
  var peg$c31 = "sort-by";
  var peg$c32 = "sortOrder";
  var peg$c33 = "sort-order";
  var peg$c34 = "policyID";
  var peg$c35 = "workspace";
  var peg$c36 = "submitted";
  var peg$c37 = "approved";
  var peg$c38 = "paid";
  var peg$c39 = "exported";
  var peg$c40 = "posted";
  var peg$c41 = "withdrawn";
  var peg$c42 = "groupby";
  var peg$c43 = "group-by";
  var peg$c44 = "feed";
  var peg$c45 = "title";
  var peg$c46 = "assignee";
  var peg$c47 = "createdby";
  var peg$c48 = "created-by";
  var peg$c49 = "action";
  var peg$c50 = "perdiem";
  var peg$c51 = "per-diem";
  var peg$c52 = "!=";
  var peg$c53 = ">=";
  var peg$c54 = ">";
  var peg$c55 = "<=";
  var peg$c56 = "<";
  var peg$c57 = "\u201C";
  var peg$c58 = "\u201D";
  var peg$c59 = "\"";

  var peg$r0 = /^[:=]/;
  var peg$r1 = /^[^ ,\t\n\r\xA0]/;
  var peg$r2 = /^[ \t\r\n\xA0]/;
  var peg$r3 = /^[^ ,"\u201D\u201C\t\n\r\xA0]/;
  var peg$r4 = /^["\u201C-\u201D]/;
  var peg$r5 = /^[^"\u201D\u201C\r\n]/;
  var peg$r6 = /^[ \t\n\r\xA0a-zA-Z0-9]/;
  var peg$r7 = /^[a-zA-Z0-9]/;
  var peg$r8 = /^[ \t\n\r\xA0]/;
  var peg$r9 = /^[ \t\n\r\xA0a-zA-Z]/;
  var peg$r10 = /^[,]/;

  var peg$e0 = peg$literalExpectation(",", false);
  var peg$e1 = peg$otherExpectation("key");
  var peg$e2 = peg$literalExpectation("date", true);
  var peg$e3 = peg$literalExpectation("amount", true);
  var peg$e4 = peg$literalExpectation("merchant", true);
  var peg$e5 = peg$literalExpectation("description", true);
  var peg$e6 = peg$literalExpectation("reportid", true);
  var peg$e7 = peg$literalExpectation("report-id", true);
  var peg$e8 = peg$literalExpectation("keyword", true);
  var peg$e9 = peg$literalExpectation("in", true);
  var peg$e10 = peg$literalExpectation("currency", true);
  var peg$e11 = peg$literalExpectation("groupCurrency", false);
  var peg$e12 = peg$literalExpectation("group-currency", true);
  var peg$e13 = peg$literalExpectation("tag", true);
  var peg$e14 = peg$literalExpectation("category", true);
  var peg$e15 = peg$literalExpectation("to", true);
  var peg$e16 = peg$literalExpectation("exporter", true);
  var peg$e17 = peg$literalExpectation("payer", true);
  var peg$e18 = peg$literalExpectation("taxRate", true);
  var peg$e19 = peg$literalExpectation("tax-rate", true);
  var peg$e20 = peg$literalExpectation("cardID", false);
  var peg$e21 = peg$literalExpectation("card", true);
  var peg$e22 = peg$literalExpectation("from", true);
  var peg$e23 = peg$literalExpectation("expenseType", false);
  var peg$e24 = peg$literalExpectation("expense-type", true);
  var peg$e25 = peg$literalExpectation("withdrawalType", false);
  var peg$e26 = peg$literalExpectation("withdrawal-type", true);
  var peg$e27 = peg$literalExpectation("billable", true);
  var peg$e28 = peg$literalExpectation("reimbursable", true);
  var peg$e29 = peg$literalExpectation("type", true);
  var peg$e30 = peg$literalExpectation("status", true);
  var peg$e31 = peg$literalExpectation("sortBy", false);
  var peg$e32 = peg$literalExpectation("sort-by", true);
  var peg$e33 = peg$literalExpectation("sortOrder", false);
  var peg$e34 = peg$literalExpectation("sort-order", true);
  var peg$e35 = peg$literalExpectation("policyID", false);
  var peg$e36 = peg$literalExpectation("workspace", true);
  var peg$e37 = peg$literalExpectation("submitted", true);
  var peg$e38 = peg$literalExpectation("approved", true);
  var peg$e39 = peg$literalExpectation("paid", true);
  var peg$e40 = peg$literalExpectation("exported", true);
  var peg$e41 = peg$literalExpectation("posted", true);
  var peg$e42 = peg$literalExpectation("withdrawn", true);
  var peg$e43 = peg$literalExpectation("groupBy", true);
  var peg$e44 = peg$literalExpectation("group-by", true);
  var peg$e45 = peg$literalExpectation("feed", true);
  var peg$e46 = peg$literalExpectation("title", true);
  var peg$e47 = peg$literalExpectation("assignee", true);
  var peg$e48 = peg$literalExpectation("createdBy", true);
  var peg$e49 = peg$literalExpectation("created-by", true);
  var peg$e50 = peg$literalExpectation("action", true);
  var peg$e51 = peg$literalExpectation("perDiem", true);
  var peg$e52 = peg$literalExpectation("per-diem", true);
  var peg$e53 = peg$otherExpectation("operator");
  var peg$e54 = peg$classExpectation([":", "="], false, false);
  var peg$e55 = peg$literalExpectation("!=", false);
  var peg$e56 = peg$literalExpectation(">=", false);
  var peg$e57 = peg$literalExpectation(">", false);
  var peg$e58 = peg$literalExpectation("<=", false);
  var peg$e59 = peg$literalExpectation("<", false);
  var peg$e60 = peg$otherExpectation("word");
  var peg$e61 = peg$classExpectation([" ", ",", "\t", "\n", "\r", "\xA0"], true, false);
  var peg$e62 = peg$otherExpectation("whitespace");
  var peg$e63 = peg$classExpectation([" ", "\t", "\r", "\n", "\xA0"], false, false);
  var peg$e64 = peg$otherExpectation("quote");
  var peg$e65 = peg$classExpectation([" ", ",", "\"", "\u201D", "\u201C", "\t", "\n", "\r", "\xA0"], true, false);
  var peg$e66 = peg$classExpectation(["\"", ["\u201C", "\u201D"]], false, false);
  var peg$e67 = peg$classExpectation(["\"", "\u201D", "\u201C", "\r", "\n"], true, false);
  var peg$e68 = peg$literalExpectation("\u201C", false);
  var peg$e69 = peg$literalExpectation("\u201D", false);
  var peg$e70 = peg$literalExpectation("\"", false);
  var peg$e71 = peg$classExpectation([" ", "\t", "\n", "\r", "\xA0", ["a", "z"], ["A", "Z"], ["0", "9"]], false, false);
  var peg$e72 = peg$classExpectation([["a", "z"], ["A", "Z"], ["0", "9"]], false, false);
  var peg$e73 = peg$classExpectation([" ", "\t", "\n", "\r", "\xA0"], false, false);
  var peg$e74 = peg$classExpectation([" ", "\t", "\n", "\r", "\xA0", ["a", "z"], ["A", "Z"]], false, false);
  var peg$e75 = peg$anyExpectation();
  var peg$e76 = peg$classExpectation([","], false, false);

  var peg$f0 = function(ranges) { return { autocomplete, ranges }; };
  var peg$f1 = function(filters) { return filters.filter(Boolean).flat(); };
  var peg$f2 = function(key, op, value) {
      expectingNestedQuote = false; nameOperator = false; // Reset string parser

      if (!value) {
        autocomplete = {
          key,
          value: "",
          start: location().end.offset,
          length: 0,
        };
        return;
      }

      autocomplete = {
        key,
        ...value[value.length - 1],
      };
      return value
        .filter((filter) => filter.length > 0)
        .map((filter) => ({
          key,
          ...filter,
        }));
    };
  var peg$f3 = function() { autocomplete = null; };
  var peg$f4 = function(k) {
      nameOperator = (k === "from" || k === "to");
      return k;
};
  var peg$f5 = function(parts, empty) {
      const ends = location();
      const value = parts.flat().filter(Boolean); // Filter out undefined values returned by the predicate
      if (empty) {
        value.push("");
      }
      let count = ends.start.offset;
      const result = [];
      value.forEach((filter) => {
        let word = filter;
        if (word.startsWith('"') && word.endsWith('"') && word.length >= 2) {
          word = word.slice(1, -1);
        }
        result.push({
          value: word,
          start: count,
          length: filter.length,
        });
        count += filter.length + 1;
      });
      return result;
    };
  var peg$f6 = function() { return "date"; };
  var peg$f7 = function() { return "amount"; };
  var peg$f8 = function() { return "merchant"; };
  var peg$f9 = function() { return "description"; };
  var peg$f10 = function() { return "reportID"; };
  var peg$f11 = function() { return "keyword"; };
  var peg$f12 = function() { return "in"; };
  var peg$f13 = function() { return "currency"; };
  var peg$f14 = function() { return "groupCurrency"; };
  var peg$f15 = function() { return "tag"; };
  var peg$f16 = function() { return "category"; };
  var peg$f17 = function() { return "to"; };
  var peg$f18 = function() { return "exporter"; };
  var peg$f19 = function() { return "payer"; };
  var peg$f20 = function() { return "taxRate"; };
  var peg$f21 = function() { return "cardID"; };
  var peg$f22 = function() { return "from"; };
  var peg$f23 = function() { return "expenseType"; };
  var peg$f24 = function() { return "withdrawalType"; };
  var peg$f25 = function() { return "billable"; };
  var peg$f26 = function() { return "reimbursable"; };
  var peg$f27 = function() { return "type"; };
  var peg$f28 = function() { return "status"; };
  var peg$f29 = function() { return "sortBy"; };
  var peg$f30 = function() { return "sortOrder"; };
  var peg$f31 = function() { return "policyID"; };
  var peg$f32 = function() { return "submitted"; };
  var peg$f33 = function() { return "approved"; };
  var peg$f34 = function() { return "paid"; };
  var peg$f35 = function() { return "exported"; };
  var peg$f36 = function() { return "posted"; };
  var peg$f37 = function() { return "withdrawn"; };
  var peg$f38 = function() { return "groupBy"; };
  var peg$f39 = function() { return "feed"; };
  var peg$f40 = function() { return "title"; };
  var peg$f41 = function() { return "assignee"; };
  var peg$f42 = function() { return "createdBy"; };
  var peg$f43 = function() { return "action"; };
  var peg$f44 = function() { return "perDiem"; };
  var peg$f45 = function() { return "eq"; };
  var peg$f46 = function() { return "neq"; };
  var peg$f47 = function() { return "gte"; };
  var peg$f48 = function() { return "gt"; };
  var peg$f49 = function() { return "lte"; };
  var peg$f50 = function() { return "lt"; };
  var peg$f51 = function(o) {
      if (nameOperator) {
        expectingNestedQuote = (o === "eq"); // Use simple parser if no valid operator is found
      }
      return o;
  };
  var peg$f52 = function(chars) { return chars.join("").trim(); };
  var peg$f53 = function() { return "and"; };
  var peg$f54 = function() { return expectingNestedQuote; };
  var peg$f55 = function(start, inner, end) { //handle no-breaking space
      return [...start, '"', ...inner, '"', ...end].join("");
    };
  var peg$f56 = function(start) {return "“"};
  var peg$f57 = function(start) {return "”"};
  var peg$f58 = function(start) {return "\""};
  var peg$f59 = function(start, inner, end) {
    return [...start, '"', ...inner, '"'].join("");
};
  var peg$currPos = options.peg$currPos | 0;
  var peg$savedPos = peg$currPos;
  var peg$posDetailsCache = [{ line: 1, column: 1 }];
  var peg$maxFailPos = peg$currPos;
  var peg$maxFailExpected = options.peg$maxFailExpected || [];
  var peg$silentFails = options.peg$silentFails | 0;

  var peg$result;

  if (options.startRule) {
    if (!(options.startRule in peg$startRuleFunctions)) {
      throw new Error("Can't start parsing from rule \"" + options.startRule + "\".");
    }

    peg$startRuleFunction = peg$startRuleFunctions[options.startRule];
  }

  function text() {
    return input.substring(peg$savedPos, peg$currPos);
  }

  function offset() {
    return peg$savedPos;
  }

  function range() {
    return {
      source: peg$source,
      start: peg$savedPos,
      end: peg$currPos
    };
  }

  function location() {
    return peg$computeLocation(peg$savedPos, peg$currPos);
  }

  function expected(description, location) {
    location = location !== undefined
      ? location
      : peg$computeLocation(peg$savedPos, peg$currPos);

    throw peg$buildStructuredError(
      [peg$otherExpectation(description)],
      input.substring(peg$savedPos, peg$currPos),
      location
    );
  }

  function error(message, location) {
    location = location !== undefined
      ? location
      : peg$computeLocation(peg$savedPos, peg$currPos);

    throw peg$buildSimpleError(message, location);
  }

  function peg$literalExpectation(text, ignoreCase) {
    return { type: "literal", text: text, ignoreCase: ignoreCase };
  }

  function peg$classExpectation(parts, inverted, ignoreCase) {
    return { type: "class", parts: parts, inverted: inverted, ignoreCase: ignoreCase };
  }

  function peg$anyExpectation() {
    return { type: "any" };
  }

  function peg$endExpectation() {
    return { type: "end" };
  }

  function peg$otherExpectation(description) {
    return { type: "other", description: description };
  }

  function peg$computePosDetails(pos) {
    var details = peg$posDetailsCache[pos];
    var p;

    if (details) {
      return details;
    } else {
      if (pos >= peg$posDetailsCache.length) {
        p = peg$posDetailsCache.length - 1;
      } else {
        p = pos;
        while (!peg$posDetailsCache[--p]) {}
      }

      details = peg$posDetailsCache[p];
      details = {
        line: details.line,
        column: details.column
      };

      while (p < pos) {
        if (input.charCodeAt(p) === 10) {
          details.line++;
          details.column = 1;
        } else {
          details.column++;
        }

        p++;
      }

      peg$posDetailsCache[pos] = details;

      return details;
    }
  }

  function peg$computeLocation(startPos, endPos, offset) {
    var startPosDetails = peg$computePosDetails(startPos);
    var endPosDetails = peg$computePosDetails(endPos);

    var res = {
      source: peg$source,
      start: {
        offset: startPos,
        line: startPosDetails.line,
        column: startPosDetails.column
      },
      end: {
        offset: endPos,
        line: endPosDetails.line,
        column: endPosDetails.column
      }
    };
    if (offset && peg$source && (typeof peg$source.offset === "function")) {
      res.start = peg$source.offset(res.start);
      res.end = peg$source.offset(res.end);
    }
    return res;
  }

  function peg$fail(expected) {
    if (peg$currPos < peg$maxFailPos) { return; }

    if (peg$currPos > peg$maxFailPos) {
      peg$maxFailPos = peg$currPos;
      peg$maxFailExpected = [];
    }

    peg$maxFailExpected.push(expected);
  }

  function peg$buildSimpleError(message, location) {
    return new peg$SyntaxError(message, null, null, location);
  }

  function peg$buildStructuredError(expected, found, location) {
    return new peg$SyntaxError(
      peg$SyntaxError.buildMessage(expected, found),
      expected,
      found,
      location
    );
  }

  function peg$parsequery() {
    var s0, s1, s2, s3;

    s0 = peg$currPos;
    s1 = peg$parse_();
    s2 = peg$parsefilterList();
    s3 = peg$parse_();
    peg$savedPos = s0;
    s0 = peg$f0(s2);

    return s0;
  }

  function peg$parsefilterList() {
    var s0, s1, s2, s3;

    s0 = peg$currPos;
    s1 = [];
    s2 = peg$parsefilter();
    while (s2 !== peg$FAILED) {
      s1.push(s2);
      s2 = peg$currPos;
      s3 = peg$parselogicalAnd();
      s3 = peg$parsefilter();
      if (s3 === peg$FAILED) {
        peg$currPos = s2;
        s2 = peg$FAILED;
      } else {
        s2 = s3;
      }
    }
    peg$savedPos = s0;
    s1 = peg$f1(s1);
    s0 = s1;

    return s0;
  }

  function peg$parsefilter() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = peg$parsedefaultFilter();
    if (s1 === peg$FAILED) {
      s1 = peg$parsefreeTextFilter();
    }
    if (s1 !== peg$FAILED) {
      s0 = s1;
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }

    return s0;
  }

  function peg$parsedefaultFilter() {
    var s0, s1, s2, s3, s4, s5, s6;

    s0 = peg$currPos;
    s1 = peg$parse_();
    s2 = peg$parsefilterKey();
    if (s2 !== peg$FAILED) {
      s3 = peg$parse_();
      s4 = peg$parsefilterOperator();
      if (s4 !== peg$FAILED) {
        s5 = peg$parse_();
        s6 = peg$parseidentifier();
        if (s6 === peg$FAILED) {
          s6 = null;
        }
        peg$savedPos = s0;
        s0 = peg$f2(s2, s4, s6);
      } else {
        peg$currPos = s0;
        s0 = peg$FAILED;
      }
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }

    return s0;
  }

  function peg$parsefreeTextFilter() {
    var s0, s1, s2, s3;

    s0 = peg$currPos;
    s1 = peg$parse_();
    s2 = peg$parseidentifier();
    if (s2 === peg$FAILED) {
      if (input.charCodeAt(peg$currPos) === 44) {
        s2 = peg$c0;
        peg$currPos++;
      } else {
        s2 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e0); }
      }
    }
    if (s2 !== peg$FAILED) {
      s3 = peg$parse_();
      peg$savedPos = s0;
      s0 = peg$f3();
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }

    return s0;
  }

  function peg$parseautocompleteKey() {
    var s0, s1;

    peg$silentFails++;
    s0 = peg$currPos;
    s1 = peg$parsein();
    if (s1 === peg$FAILED) {
      s1 = peg$parsecurrency();
      if (s1 === peg$FAILED) {
        s1 = peg$parsegroupCurrency();
        if (s1 === peg$FAILED) {
          s1 = peg$parsetag();
          if (s1 === peg$FAILED) {
            s1 = peg$parsecategory();
            if (s1 === peg$FAILED) {
              s1 = peg$parseto();
              if (s1 === peg$FAILED) {
                s1 = peg$parsetaxRate();
                if (s1 === peg$FAILED) {
                  s1 = peg$parsefrom();
                  if (s1 === peg$FAILED) {
                    s1 = peg$parsepayer();
                    if (s1 === peg$FAILED) {
                      s1 = peg$parseexporter();
                      if (s1 === peg$FAILED) {
                        s1 = peg$parsecreatedBy();
                        if (s1 === peg$FAILED) {
                          s1 = peg$parseassignee();
                          if (s1 === peg$FAILED) {
                            s1 = peg$parseexpenseType();
                            if (s1 === peg$FAILED) {
                              s1 = peg$parsewithdrawalType();
                              if (s1 === peg$FAILED) {
                                s1 = peg$parsetype();
                                if (s1 === peg$FAILED) {
                                  s1 = peg$parsestatus();
                                  if (s1 === peg$FAILED) {
                                    s1 = peg$parsecardID();
                                    if (s1 === peg$FAILED) {
                                      s1 = peg$parsefeed();
                                      if (s1 === peg$FAILED) {
                                        s1 = peg$parsegroupBy();
                                        if (s1 === peg$FAILED) {
                                          s1 = peg$parsereimbursable();
                                          if (s1 === peg$FAILED) {
                                            s1 = peg$parsebillable();
                                            if (s1 === peg$FAILED) {
                                              s1 = peg$parsepolicyID();
                                              if (s1 === peg$FAILED) {
                                                s1 = peg$parseaction();
                                                if (s1 === peg$FAILED) {
                                                  s1 = peg$parsedate();
                                                  if (s1 === peg$FAILED) {
                                                    s1 = peg$parsesubmitted();
                                                    if (s1 === peg$FAILED) {
                                                      s1 = peg$parseapproved();
                                                      if (s1 === peg$FAILED) {
                                                        s1 = peg$parsepaid();
                                                        if (s1 === peg$FAILED) {
                                                          s1 = peg$parseexported();
                                                          if (s1 === peg$FAILED) {
                                                            s1 = peg$parsewithdrawn();
                                                            if (s1 === peg$FAILED) {
                                                              s1 = peg$parseposted();
                                                            }
                                                          }
                                                        }
                                                      }
                                                    }
                                                  }
                                                }
                                              }
                                            }
                                          }
                                        }
                                      }
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    if (s1 !== peg$FAILED) {
      s0 = s1;
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e1); }
    }

    return s0;
  }

  function peg$parsefilterKey() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = peg$parseautocompleteKey();
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f4(s1);
    }
    s0 = s1;

    return s0;
  }

  function peg$parseidentifier() {
    var s0, s1, s2, s3, s4;

    s0 = peg$currPos;
    s1 = peg$currPos;
    s2 = [];
    s3 = peg$parsequotedString();
    if (s3 === peg$FAILED) {
      s3 = peg$parsealphanumeric();
    }
    while (s3 !== peg$FAILED) {
      s2.push(s3);
      s3 = peg$currPos;
      if (input.charCodeAt(peg$currPos) === 44) {
        s4 = peg$c0;
        peg$currPos++;
      } else {
        s4 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e0); }
      }
      if (s4 !== peg$FAILED) {
        s4 = peg$parsequotedString();
        if (s4 === peg$FAILED) {
          s4 = peg$parsealphanumeric();
        }
        if (s4 === peg$FAILED) {
          peg$currPos = s3;
          s3 = peg$FAILED;
        } else {
          s3 = s4;
        }
      } else {
        s3 = s4;
      }
    }
    if (s2.length < 1) {
      peg$currPos = s1;
      s1 = peg$FAILED;
    } else {
      s1 = s2;
    }
    if (s1 !== peg$FAILED) {
      if (input.charCodeAt(peg$currPos) === 44) {
        s2 = peg$c0;
        peg$currPos++;
      } else {
        s2 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e0); }
      }
      if (s2 === peg$FAILED) {
        s2 = null;
      }
      peg$savedPos = s0;
      s0 = peg$f5(s1, s2);
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }

    return s0;
  }

  function peg$parsedate() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 4);
    if (s1.toLowerCase() === peg$c1) {
      peg$currPos += 4;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e2); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f6();
    }
    s0 = s1;

    return s0;
  }

  function peg$parseamount() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 6);
    if (s1.toLowerCase() === peg$c2) {
      peg$currPos += 6;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e3); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f7();
    }
    s0 = s1;

    return s0;
  }

  function peg$parsemerchant() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 8);
    if (s1.toLowerCase() === peg$c3) {
      peg$currPos += 8;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e4); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f8();
    }
    s0 = s1;

    return s0;
  }

  function peg$parsedescription() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 11);
    if (s1.toLowerCase() === peg$c4) {
      peg$currPos += 11;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e5); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f9();
    }
    s0 = s1;

    return s0;
  }

  function peg$parsereportID() {
    var s0, s1;

    s0 = input.substr(peg$currPos, 8);
    if (s0.toLowerCase() === peg$c5) {
      peg$currPos += 8;
    } else {
      s0 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e6); }
    }
    if (s0 === peg$FAILED) {
      s0 = peg$currPos;
      s1 = input.substr(peg$currPos, 9);
      if (s1.toLowerCase() === peg$c6) {
        peg$currPos += 9;
      } else {
        s1 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e7); }
      }
      if (s1 !== peg$FAILED) {
        peg$savedPos = s0;
        s1 = peg$f10();
      }
      s0 = s1;
    }

    return s0;
  }

  function peg$parsekeyword() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 7);
    if (s1.toLowerCase() === peg$c7) {
      peg$currPos += 7;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e8); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f11();
    }
    s0 = s1;

    return s0;
  }

  function peg$parsein() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 2);
    if (s1.toLowerCase() === peg$c8) {
      peg$currPos += 2;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e9); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f12();
    }
    s0 = s1;

    return s0;
  }

  function peg$parsecurrency() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 8);
    if (s1.toLowerCase() === peg$c9) {
      peg$currPos += 8;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e10); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f13();
    }
    s0 = s1;

    return s0;
  }

  function peg$parsegroupCurrency() {
    var s0, s1;

    if (input.substr(peg$currPos, 13) === peg$c10) {
      s0 = peg$c10;
      peg$currPos += 13;
    } else {
      s0 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e11); }
    }
    if (s0 === peg$FAILED) {
      s0 = peg$currPos;
      s1 = input.substr(peg$currPos, 14);
      if (s1.toLowerCase() === peg$c11) {
        peg$currPos += 14;
      } else {
        s1 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e12); }
      }
      if (s1 !== peg$FAILED) {
        peg$savedPos = s0;
        s1 = peg$f14();
      }
      s0 = s1;
    }

    return s0;
  }

  function peg$parsetag() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 3);
    if (s1.toLowerCase() === peg$c12) {
      peg$currPos += 3;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e13); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f15();
    }
    s0 = s1;

    return s0;
  }

  function peg$parsecategory() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 8);
    if (s1.toLowerCase() === peg$c13) {
      peg$currPos += 8;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e14); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f16();
    }
    s0 = s1;

    return s0;
  }

  function peg$parseto() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 2);
    if (s1.toLowerCase() === peg$c14) {
      peg$currPos += 2;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e15); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f17();
    }
    s0 = s1;

    return s0;
  }

  function peg$parseexporter() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 8);
    if (s1.toLowerCase() === peg$c15) {
      peg$currPos += 8;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e16); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f18();
    }
    s0 = s1;

    return s0;
  }

  function peg$parsepayer() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 5);
    if (s1.toLowerCase() === peg$c16) {
      peg$currPos += 5;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e17); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f19();
    }
    s0 = s1;

    return s0;
  }

  function peg$parsetaxRate() {
    var s0, s1;

    s0 = input.substr(peg$currPos, 7);
    if (s0.toLowerCase() === peg$c17) {
      peg$currPos += 7;
    } else {
      s0 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e18); }
    }
    if (s0 === peg$FAILED) {
      s0 = peg$currPos;
      s1 = input.substr(peg$currPos, 8);
      if (s1.toLowerCase() === peg$c18) {
        peg$currPos += 8;
      } else {
        s1 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e19); }
      }
      if (s1 !== peg$FAILED) {
        peg$savedPos = s0;
        s1 = peg$f20();
      }
      s0 = s1;
    }

    return s0;
  }

  function peg$parsecardID() {
    var s0, s1;

    if (input.substr(peg$currPos, 6) === peg$c19) {
      s0 = peg$c19;
      peg$currPos += 6;
    } else {
      s0 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e20); }
    }
    if (s0 === peg$FAILED) {
      s0 = peg$currPos;
      s1 = input.substr(peg$currPos, 4);
      if (s1.toLowerCase() === peg$c20) {
        peg$currPos += 4;
      } else {
        s1 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e21); }
      }
      if (s1 !== peg$FAILED) {
        peg$savedPos = s0;
        s1 = peg$f21();
      }
      s0 = s1;
    }

    return s0;
  }

  function peg$parsefrom() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 4);
    if (s1.toLowerCase() === peg$c21) {
      peg$currPos += 4;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e22); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f22();
    }
    s0 = s1;

    return s0;
  }

  function peg$parseexpenseType() {
    var s0, s1;

    if (input.substr(peg$currPos, 11) === peg$c22) {
      s0 = peg$c22;
      peg$currPos += 11;
    } else {
      s0 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e23); }
    }
    if (s0 === peg$FAILED) {
      s0 = peg$currPos;
      s1 = input.substr(peg$currPos, 12);
      if (s1.toLowerCase() === peg$c23) {
        peg$currPos += 12;
      } else {
        s1 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e24); }
      }
      if (s1 !== peg$FAILED) {
        peg$savedPos = s0;
        s1 = peg$f23();
      }
      s0 = s1;
    }

    return s0;
  }

  function peg$parsewithdrawalType() {
    var s0, s1;

    if (input.substr(peg$currPos, 14) === peg$c24) {
      s0 = peg$c24;
      peg$currPos += 14;
    } else {
      s0 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e25); }
    }
    if (s0 === peg$FAILED) {
      s0 = peg$currPos;
      s1 = input.substr(peg$currPos, 15);
      if (s1.toLowerCase() === peg$c25) {
        peg$currPos += 15;
      } else {
        s1 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e26); }
      }
      if (s1 !== peg$FAILED) {
        peg$savedPos = s0;
        s1 = peg$f24();
      }
      s0 = s1;
    }

    return s0;
  }

  function peg$parsebillable() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 8);
    if (s1.toLowerCase() === peg$c26) {
      peg$currPos += 8;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e27); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f25();
    }
    s0 = s1;

    return s0;
  }

  function peg$parsereimbursable() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 12);
    if (s1.toLowerCase() === peg$c27) {
      peg$currPos += 12;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e28); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f26();
    }
    s0 = s1;

    return s0;
  }

  function peg$parsetype() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 4);
    if (s1.toLowerCase() === peg$c28) {
      peg$currPos += 4;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e29); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f27();
    }
    s0 = s1;

    return s0;
  }

  function peg$parsestatus() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 6);
    if (s1.toLowerCase() === peg$c29) {
      peg$currPos += 6;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e30); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f28();
    }
    s0 = s1;

    return s0;
  }

  function peg$parsesortBy() {
    var s0, s1;

    if (input.substr(peg$currPos, 6) === peg$c30) {
      s0 = peg$c30;
      peg$currPos += 6;
    } else {
      s0 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e31); }
    }
    if (s0 === peg$FAILED) {
      s0 = peg$currPos;
      s1 = input.substr(peg$currPos, 7);
      if (s1.toLowerCase() === peg$c31) {
        peg$currPos += 7;
      } else {
        s1 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e32); }
      }
      if (s1 !== peg$FAILED) {
        peg$savedPos = s0;
        s1 = peg$f29();
      }
      s0 = s1;
    }

    return s0;
  }

  function peg$parsesortOrder() {
    var s0, s1;

    if (input.substr(peg$currPos, 9) === peg$c32) {
      s0 = peg$c32;
      peg$currPos += 9;
    } else {
      s0 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e33); }
    }
    if (s0 === peg$FAILED) {
      s0 = peg$currPos;
      s1 = input.substr(peg$currPos, 10);
      if (s1.toLowerCase() === peg$c33) {
        peg$currPos += 10;
      } else {
        s1 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e34); }
      }
      if (s1 !== peg$FAILED) {
        peg$savedPos = s0;
        s1 = peg$f30();
      }
      s0 = s1;
    }

    return s0;
  }

  function peg$parsepolicyID() {
    var s0, s1;

    if (input.substr(peg$currPos, 8) === peg$c34) {
      s0 = peg$c34;
      peg$currPos += 8;
    } else {
      s0 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e35); }
    }
    if (s0 === peg$FAILED) {
      s0 = peg$currPos;
      s1 = input.substr(peg$currPos, 9);
      if (s1.toLowerCase() === peg$c35) {
        peg$currPos += 9;
      } else {
        s1 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e36); }
      }
      if (s1 !== peg$FAILED) {
        peg$savedPos = s0;
        s1 = peg$f31();
      }
      s0 = s1;
    }

    return s0;
  }

  function peg$parsesubmitted() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 9);
    if (s1.toLowerCase() === peg$c36) {
      peg$currPos += 9;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e37); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f32();
    }
    s0 = s1;

    return s0;
  }

  function peg$parseapproved() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 8);
    if (s1.toLowerCase() === peg$c37) {
      peg$currPos += 8;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e38); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f33();
    }
    s0 = s1;

    return s0;
  }

  function peg$parsepaid() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 4);
    if (s1.toLowerCase() === peg$c38) {
      peg$currPos += 4;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e39); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f34();
    }
    s0 = s1;

    return s0;
  }

  function peg$parseexported() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 8);
    if (s1.toLowerCase() === peg$c39) {
      peg$currPos += 8;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e40); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f35();
    }
    s0 = s1;

    return s0;
  }

  function peg$parseposted() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 6);
    if (s1.toLowerCase() === peg$c40) {
      peg$currPos += 6;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e41); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f36();
    }
    s0 = s1;

    return s0;
  }

  function peg$parsewithdrawn() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 9);
    if (s1.toLowerCase() === peg$c41) {
      peg$currPos += 9;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e42); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f37();
    }
    s0 = s1;

    return s0;
  }

  function peg$parsegroupBy() {
    var s0, s1;

    s0 = input.substr(peg$currPos, 7);
    if (s0.toLowerCase() === peg$c42) {
      peg$currPos += 7;
    } else {
      s0 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e43); }
    }
    if (s0 === peg$FAILED) {
      s0 = peg$currPos;
      s1 = input.substr(peg$currPos, 8);
      if (s1.toLowerCase() === peg$c43) {
        peg$currPos += 8;
      } else {
        s1 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e44); }
      }
      if (s1 !== peg$FAILED) {
        peg$savedPos = s0;
        s1 = peg$f38();
      }
      s0 = s1;
    }

    return s0;
  }

  function peg$parsefeed() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 4);
    if (s1.toLowerCase() === peg$c44) {
      peg$currPos += 4;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e45); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f39();
    }
    s0 = s1;

    return s0;
  }

  function peg$parsetitle() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 5);
    if (s1.toLowerCase() === peg$c45) {
      peg$currPos += 5;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e46); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f40();
    }
    s0 = s1;

    return s0;
  }

  function peg$parseassignee() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 8);
    if (s1.toLowerCase() === peg$c46) {
      peg$currPos += 8;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e47); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f41();
    }
    s0 = s1;

    return s0;
  }

  function peg$parsecreatedBy() {
    var s0, s1;

    s0 = input.substr(peg$currPos, 9);
    if (s0.toLowerCase() === peg$c47) {
      peg$currPos += 9;
    } else {
      s0 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e48); }
    }
    if (s0 === peg$FAILED) {
      s0 = peg$currPos;
      s1 = input.substr(peg$currPos, 10);
      if (s1.toLowerCase() === peg$c48) {
        peg$currPos += 10;
      } else {
        s1 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e49); }
      }
      if (s1 !== peg$FAILED) {
        peg$savedPos = s0;
        s1 = peg$f42();
      }
      s0 = s1;
    }

    return s0;
  }

  function peg$parseaction() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = input.substr(peg$currPos, 6);
    if (s1.toLowerCase() === peg$c49) {
      peg$currPos += 6;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e50); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f43();
    }
    s0 = s1;

    return s0;
  }

  function peg$parseperDiem() {
    var s0, s1;

    s0 = input.substr(peg$currPos, 7);
    if (s0.toLowerCase() === peg$c50) {
      peg$currPos += 7;
    } else {
      s0 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e51); }
    }
    if (s0 === peg$FAILED) {
      s0 = peg$currPos;
      s1 = input.substr(peg$currPos, 8);
      if (s1.toLowerCase() === peg$c51) {
        peg$currPos += 8;
      } else {
        s1 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e52); }
      }
      if (s1 !== peg$FAILED) {
        peg$savedPos = s0;
        s1 = peg$f44();
      }
      s0 = s1;
    }

    return s0;
  }

  function peg$parseoperator() {
    var s0, s1;

    peg$silentFails++;
    s0 = peg$currPos;
    s1 = input.charAt(peg$currPos);
    if (peg$r0.test(s1)) {
      peg$currPos++;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e54); }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f45();
    }
    s0 = s1;
    if (s0 === peg$FAILED) {
      s0 = peg$currPos;
      if (input.substr(peg$currPos, 2) === peg$c52) {
        s1 = peg$c52;
        peg$currPos += 2;
      } else {
        s1 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e55); }
      }
      if (s1 !== peg$FAILED) {
        peg$savedPos = s0;
        s1 = peg$f46();
      }
      s0 = s1;
      if (s0 === peg$FAILED) {
        s0 = peg$currPos;
        if (input.substr(peg$currPos, 2) === peg$c53) {
          s1 = peg$c53;
          peg$currPos += 2;
        } else {
          s1 = peg$FAILED;
          if (peg$silentFails === 0) { peg$fail(peg$e56); }
        }
        if (s1 !== peg$FAILED) {
          peg$savedPos = s0;
          s1 = peg$f47();
        }
        s0 = s1;
        if (s0 === peg$FAILED) {
          s0 = peg$currPos;
          if (input.charCodeAt(peg$currPos) === 62) {
            s1 = peg$c54;
            peg$currPos++;
          } else {
            s1 = peg$FAILED;
            if (peg$silentFails === 0) { peg$fail(peg$e57); }
          }
          if (s1 !== peg$FAILED) {
            peg$savedPos = s0;
            s1 = peg$f48();
          }
          s0 = s1;
          if (s0 === peg$FAILED) {
            s0 = peg$currPos;
            if (input.substr(peg$currPos, 2) === peg$c55) {
              s1 = peg$c55;
              peg$currPos += 2;
            } else {
              s1 = peg$FAILED;
              if (peg$silentFails === 0) { peg$fail(peg$e58); }
            }
            if (s1 !== peg$FAILED) {
              peg$savedPos = s0;
              s1 = peg$f49();
            }
            s0 = s1;
            if (s0 === peg$FAILED) {
              s0 = peg$currPos;
              if (input.charCodeAt(peg$currPos) === 60) {
                s1 = peg$c56;
                peg$currPos++;
              } else {
                s1 = peg$FAILED;
                if (peg$silentFails === 0) { peg$fail(peg$e59); }
              }
              if (s1 !== peg$FAILED) {
                peg$savedPos = s0;
                s1 = peg$f50();
              }
              s0 = s1;
            }
          }
        }
      }
    }
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e53); }
    }

    return s0;
  }

  function peg$parsefilterOperator() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = peg$parseoperator();
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f51(s1);
    }
    s0 = s1;

    return s0;
  }

  function peg$parsealphanumeric() {
    var s0, s1, s2;

    peg$silentFails++;
    s0 = peg$currPos;
    s1 = [];
    s2 = input.charAt(peg$currPos);
    if (peg$r1.test(s2)) {
      peg$currPos++;
    } else {
      s2 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e61); }
    }
    if (s2 !== peg$FAILED) {
      while (s2 !== peg$FAILED) {
        s1.push(s2);
        s2 = input.charAt(peg$currPos);
        if (peg$r1.test(s2)) {
          peg$currPos++;
        } else {
          s2 = peg$FAILED;
          if (peg$silentFails === 0) { peg$fail(peg$e61); }
        }
      }
    } else {
      s1 = peg$FAILED;
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f52(s1);
    }
    s0 = s1;
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e60); }
    }

    return s0;
  }

  function peg$parselogicalAnd() {
    var s0, s1;

    s0 = peg$currPos;
    s1 = peg$parse_();
    peg$savedPos = s0;
    s1 = peg$f53();
    s0 = s1;

    return s0;
  }

  function peg$parse_() {
    var s0, s1;

    peg$silentFails++;
    s0 = [];
    s1 = input.charAt(peg$currPos);
    if (peg$r2.test(s1)) {
      peg$currPos++;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e63); }
    }
    while (s1 !== peg$FAILED) {
      s0.push(s1);
      s1 = input.charAt(peg$currPos);
      if (peg$r2.test(s1)) {
        peg$currPos++;
      } else {
        s1 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e63); }
      }
    }
    peg$silentFails--;
    s1 = peg$FAILED;
    if (peg$silentFails === 0) { peg$fail(peg$e62); }

    return s0;
  }

  function peg$parsequotedString() {
    var s0, s1, s2;

    s0 = peg$currPos;
    peg$savedPos = peg$currPos;
    s1 = peg$f54();
    if (s1) {
      s1 = undefined;
    } else {
      s1 = peg$FAILED;
    }
    if (s1 !== peg$FAILED) {
      s2 = peg$parsenestedQuotedString();
      if (s2 !== peg$FAILED) {
        s1 = [s1, s2];
        s0 = s1;
      } else {
        peg$currPos = s0;
        s0 = peg$FAILED;
      }
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }
    if (s0 === peg$FAILED) {
      s0 = peg$parsesimpleQuotedString();
    }

    return s0;
  }

  function peg$parsesimpleQuotedString() {
    var s0, s1, s2, s3, s4, s5, s6;

    peg$silentFails++;
    s0 = peg$currPos;
    s1 = [];
    s2 = input.charAt(peg$currPos);
    if (peg$r3.test(s2)) {
      peg$currPos++;
    } else {
      s2 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e65); }
    }
    while (s2 !== peg$FAILED) {
      s1.push(s2);
      s2 = input.charAt(peg$currPos);
      if (peg$r3.test(s2)) {
        peg$currPos++;
      } else {
        s2 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e65); }
      }
    }
    s2 = input.charAt(peg$currPos);
    if (peg$r4.test(s2)) {
      peg$currPos++;
    } else {
      s2 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e66); }
    }
    if (s2 !== peg$FAILED) {
      s3 = [];
      s4 = input.charAt(peg$currPos);
      if (peg$r5.test(s4)) {
        peg$currPos++;
      } else {
        s4 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e67); }
      }
      while (s4 !== peg$FAILED) {
        s3.push(s4);
        s4 = input.charAt(peg$currPos);
        if (peg$r5.test(s4)) {
          peg$currPos++;
        } else {
          s4 = peg$FAILED;
          if (peg$silentFails === 0) { peg$fail(peg$e67); }
        }
      }
      s4 = input.charAt(peg$currPos);
      if (peg$r4.test(s4)) {
        peg$currPos++;
      } else {
        s4 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e66); }
      }
      if (s4 !== peg$FAILED) {
        s5 = [];
        s6 = input.charAt(peg$currPos);
        if (peg$r1.test(s6)) {
          peg$currPos++;
        } else {
          s6 = peg$FAILED;
          if (peg$silentFails === 0) { peg$fail(peg$e61); }
        }
        while (s6 !== peg$FAILED) {
          s5.push(s6);
          s6 = input.charAt(peg$currPos);
          if (peg$r1.test(s6)) {
            peg$currPos++;
          } else {
            s6 = peg$FAILED;
            if (peg$silentFails === 0) { peg$fail(peg$e61); }
          }
        }
        peg$savedPos = s0;
        s0 = peg$f55(s1, s3, s5);
      } else {
        peg$currPos = s0;
        s0 = peg$FAILED;
      }
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e64); }
    }

    return s0;
  }

  function peg$parsenestedQuotedString() {
    var s0, s1, s2, s3, s4, s5, s6;

    peg$silentFails++;
    s0 = peg$currPos;
    s1 = [];
    s2 = input.charAt(peg$currPos);
    if (peg$r3.test(s2)) {
      peg$currPos++;
    } else {
      s2 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e65); }
    }
    while (s2 !== peg$FAILED) {
      s1.push(s2);
      s2 = input.charAt(peg$currPos);
      if (peg$r3.test(s2)) {
        peg$currPos++;
      } else {
        s2 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e65); }
      }
    }
    s2 = input.charAt(peg$currPos);
    if (peg$r4.test(s2)) {
      peg$currPos++;
    } else {
      s2 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e66); }
    }
    if (s2 !== peg$FAILED) {
      s3 = [];
      s4 = input.charAt(peg$currPos);
      if (peg$r5.test(s4)) {
        peg$currPos++;
      } else {
        s4 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e67); }
      }
      if (s4 === peg$FAILED) {
        s4 = peg$currPos;
        s5 = peg$currPos;
        peg$silentFails++;
        s6 = peg$parseclosingQuote();
        peg$silentFails--;
        if (s6 === peg$FAILED) {
          s5 = undefined;
        } else {
          peg$currPos = s5;
          s5 = peg$FAILED;
        }
        if (s5 !== peg$FAILED) {
          if (input.charCodeAt(peg$currPos) === 8220) {
            s6 = peg$c57;
            peg$currPos++;
          } else {
            s6 = peg$FAILED;
            if (peg$silentFails === 0) { peg$fail(peg$e68); }
          }
          if (s6 !== peg$FAILED) {
            peg$savedPos = s4;
            s4 = peg$f56(s1);
          } else {
            peg$currPos = s4;
            s4 = peg$FAILED;
          }
        } else {
          peg$currPos = s4;
          s4 = peg$FAILED;
        }
        if (s4 === peg$FAILED) {
          s4 = peg$currPos;
          s5 = peg$currPos;
          peg$silentFails++;
          s6 = peg$parseclosingQuote();
          peg$silentFails--;
          if (s6 === peg$FAILED) {
            s5 = undefined;
          } else {
            peg$currPos = s5;
            s5 = peg$FAILED;
          }
          if (s5 !== peg$FAILED) {
            if (input.charCodeAt(peg$currPos) === 8221) {
              s6 = peg$c58;
              peg$currPos++;
            } else {
              s6 = peg$FAILED;
              if (peg$silentFails === 0) { peg$fail(peg$e69); }
            }
            if (s6 !== peg$FAILED) {
              peg$savedPos = s4;
              s4 = peg$f57(s1);
            } else {
              peg$currPos = s4;
              s4 = peg$FAILED;
            }
          } else {
            peg$currPos = s4;
            s4 = peg$FAILED;
          }
          if (s4 === peg$FAILED) {
            s4 = peg$currPos;
            s5 = peg$currPos;
            peg$silentFails++;
            s6 = peg$parseclosingQuote();
            peg$silentFails--;
            if (s6 === peg$FAILED) {
              s5 = undefined;
            } else {
              peg$currPos = s5;
              s5 = peg$FAILED;
            }
            if (s5 !== peg$FAILED) {
              if (input.charCodeAt(peg$currPos) === 34) {
                s6 = peg$c59;
                peg$currPos++;
              } else {
                s6 = peg$FAILED;
                if (peg$silentFails === 0) { peg$fail(peg$e70); }
              }
              if (s6 !== peg$FAILED) {
                peg$savedPos = s4;
                s4 = peg$f58(s1);
              } else {
                peg$currPos = s4;
                s4 = peg$FAILED;
              }
            } else {
              peg$currPos = s4;
              s4 = peg$FAILED;
            }
          }
        }
      }
      while (s4 !== peg$FAILED) {
        s3.push(s4);
        s4 = input.charAt(peg$currPos);
        if (peg$r5.test(s4)) {
          peg$currPos++;
        } else {
          s4 = peg$FAILED;
          if (peg$silentFails === 0) { peg$fail(peg$e67); }
        }
        if (s4 === peg$FAILED) {
          s4 = peg$currPos;
          s5 = peg$currPos;
          peg$silentFails++;
          s6 = peg$parseclosingQuote();
          peg$silentFails--;
          if (s6 === peg$FAILED) {
            s5 = undefined;
          } else {
            peg$currPos = s5;
            s5 = peg$FAILED;
          }
          if (s5 !== peg$FAILED) {
            if (input.charCodeAt(peg$currPos) === 8220) {
              s6 = peg$c57;
              peg$currPos++;
            } else {
              s6 = peg$FAILED;
              if (peg$silentFails === 0) { peg$fail(peg$e68); }
            }
            if (s6 !== peg$FAILED) {
              peg$savedPos = s4;
              s4 = peg$f56(s1);
            } else {
              peg$currPos = s4;
              s4 = peg$FAILED;
            }
          } else {
            peg$currPos = s4;
            s4 = peg$FAILED;
          }
          if (s4 === peg$FAILED) {
            s4 = peg$currPos;
            s5 = peg$currPos;
            peg$silentFails++;
            s6 = peg$parseclosingQuote();
            peg$silentFails--;
            if (s6 === peg$FAILED) {
              s5 = undefined;
            } else {
              peg$currPos = s5;
              s5 = peg$FAILED;
            }
            if (s5 !== peg$FAILED) {
              if (input.charCodeAt(peg$currPos) === 8221) {
                s6 = peg$c58;
                peg$currPos++;
              } else {
                s6 = peg$FAILED;
                if (peg$silentFails === 0) { peg$fail(peg$e69); }
              }
              if (s6 !== peg$FAILED) {
                peg$savedPos = s4;
                s4 = peg$f57(s1);
              } else {
                peg$currPos = s4;
                s4 = peg$FAILED;
              }
            } else {
              peg$currPos = s4;
              s4 = peg$FAILED;
            }
            if (s4 === peg$FAILED) {
              s4 = peg$currPos;
              s5 = peg$currPos;
              peg$silentFails++;
              s6 = peg$parseclosingQuote();
              peg$silentFails--;
              if (s6 === peg$FAILED) {
                s5 = undefined;
              } else {
                peg$currPos = s5;
                s5 = peg$FAILED;
              }
              if (s5 !== peg$FAILED) {
                if (input.charCodeAt(peg$currPos) === 34) {
                  s6 = peg$c59;
                  peg$currPos++;
                } else {
                  s6 = peg$FAILED;
                  if (peg$silentFails === 0) { peg$fail(peg$e70); }
                }
                if (s6 !== peg$FAILED) {
                  peg$savedPos = s4;
                  s4 = peg$f58(s1);
                } else {
                  peg$currPos = s4;
                  s4 = peg$FAILED;
                }
              } else {
                peg$currPos = s4;
                s4 = peg$FAILED;
              }
            }
          }
        }
      }
      s4 = peg$parseclosingQuote();
      if (s4 !== peg$FAILED) {
        peg$savedPos = s0;
        s0 = peg$f59(s1, s3, s4);
      } else {
        peg$currPos = s0;
        s0 = peg$FAILED;
      }
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e64); }
    }

    return s0;
  }

  function peg$parseclosingQuote() {
    var s0, s1, s2, s3;

    s0 = peg$currPos;
    s1 = input.charAt(peg$currPos);
    if (peg$r4.test(s1)) {
      peg$currPos++;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e66); }
    }
    if (s1 !== peg$FAILED) {
      s2 = peg$currPos;
      peg$silentFails++;
      s3 = peg$parsevalidClosingCondition();
      peg$silentFails--;
      if (s3 !== peg$FAILED) {
        peg$currPos = s2;
        s2 = undefined;
      } else {
        s2 = peg$FAILED;
      }
      if (s2 !== peg$FAILED) {
        s1 = [s1, s2];
        s0 = s1;
      } else {
        peg$currPos = s0;
        s0 = peg$FAILED;
      }
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }

    return s0;
  }

  function peg$parsevalidClosingCondition() {
    var s0, s1, s2, s3, s4;

    s0 = peg$currPos;
    s1 = [];
    s2 = input.charAt(peg$currPos);
    if (peg$r6.test(s2)) {
      peg$currPos++;
    } else {
      s2 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e71); }
    }
    while (s2 !== peg$FAILED) {
      s1.push(s2);
      s2 = input.charAt(peg$currPos);
      if (peg$r6.test(s2)) {
        peg$currPos++;
      } else {
        s2 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e71); }
      }
    }
    s2 = [];
    s3 = input.charAt(peg$currPos);
    if (peg$r7.test(s3)) {
      peg$currPos++;
    } else {
      s3 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e72); }
    }
    while (s3 !== peg$FAILED) {
      s2.push(s3);
      s3 = input.charAt(peg$currPos);
      if (peg$r7.test(s3)) {
        peg$currPos++;
      } else {
        s3 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e72); }
      }
    }
    s3 = [];
    s4 = input.charAt(peg$currPos);
    if (peg$r8.test(s4)) {
      peg$currPos++;
    } else {
      s4 = peg$FAILED;
      if (peg$silentFails === 0) { peg$fail(peg$e73); }
    }
    while (s4 !== peg$FAILED) {
      s3.push(s4);
      s4 = input.charAt(peg$currPos);
      if (peg$r8.test(s4)) {
        peg$currPos++;
      } else {
        s4 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e73); }
      }
    }
    s4 = peg$parseoperator();
    if (s4 !== peg$FAILED) {
      s1 = [s1, s2, s3, s4];
      s0 = s1;
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }
    if (s0 === peg$FAILED) {
      s0 = peg$currPos;
      s1 = [];
      s2 = input.charAt(peg$currPos);
      if (peg$r9.test(s2)) {
        peg$currPos++;
      } else {
        s2 = peg$FAILED;
        if (peg$silentFails === 0) { peg$fail(peg$e74); }
      }
      while (s2 !== peg$FAILED) {
        s1.push(s2);
        s2 = input.charAt(peg$currPos);
        if (peg$r9.test(s2)) {
          peg$currPos++;
        } else {
          s2 = peg$FAILED;
          if (peg$silentFails === 0) { peg$fail(peg$e74); }
        }
      }
      s2 = peg$currPos;
      peg$silentFails++;
      s3 = peg$parseoperator();
      peg$silentFails--;
      if (s3 === peg$FAILED) {
        s2 = undefined;
      } else {
        peg$currPos = s2;
        s2 = peg$FAILED;
      }
      if (s2 !== peg$FAILED) {
        s3 = peg$currPos;
        peg$silentFails++;
        if (input.length > peg$currPos) {
          s4 = input.charAt(peg$currPos);
          peg$currPos++;
        } else {
          s4 = peg$FAILED;
          if (peg$silentFails === 0) { peg$fail(peg$e75); }
        }
        peg$silentFails--;
        if (s4 === peg$FAILED) {
          s3 = undefined;
        } else {
          peg$currPos = s3;
          s3 = peg$FAILED;
        }
        if (s3 !== peg$FAILED) {
          s1 = [s1, s2, s3];
          s0 = s1;
        } else {
          peg$currPos = s0;
          s0 = peg$FAILED;
        }
      } else {
        peg$currPos = s0;
        s0 = peg$FAILED;
      }
      if (s0 === peg$FAILED) {
        s0 = input.charAt(peg$currPos);
        if (peg$r10.test(s0)) {
          peg$currPos++;
        } else {
          s0 = peg$FAILED;
          if (peg$silentFails === 0) { peg$fail(peg$e76); }
        }
      }
    }

    return s0;
  }

 let autocomplete = null; 
 
  let nameOperator = false;
  let expectingNestedQuote = false;

  peg$result = peg$startRuleFunction();

  if (options.peg$library) {
    return /** @type {any} */ ({
      peg$result,
      peg$currPos,
      peg$FAILED,
      peg$maxFailExpected,
      peg$maxFailPos
    });
  }
  if (peg$result !== peg$FAILED && peg$currPos === input.length) {
    return peg$result;
  } else {
    if (peg$result !== peg$FAILED && peg$currPos < input.length) {
      peg$fail(peg$endExpectation());
    }

    throw peg$buildStructuredError(
      peg$maxFailExpected,
      peg$maxFailPos < input.length ? input.charAt(peg$maxFailPos) : null,
      peg$maxFailPos < input.length
        ? peg$computeLocation(peg$maxFailPos, peg$maxFailPos + 1)
        : peg$computeLocation(peg$maxFailPos, peg$maxFailPos)
    );
  }
}

const peg$allowedStartRules = [
  "query"
];

export {
  peg$allowedStartRules as StartRules,
  peg$SyntaxError as SyntaxError,
  peg$parse as parse
};
