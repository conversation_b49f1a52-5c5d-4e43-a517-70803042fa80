// This files defines the grammar that's used by [<PERSON>](https://peggyjs.org/) to generate the searchParser.js file.
// The searchParser is setup to parse our custom search syntax and output an AST with the filters.
//
// Here's a general grammar structure:
//
// query: entry point for the parser and rule to process the values returned by the filterList rule. Takes filters as an argument and returns the final AST output.
// filterList: rule to process the array of filters returned by the filter rule. It takes head and tail as arguments, filters it for null values and builds the AST.
// filter: abstract rule to simplify the filterList rule. It takes all filter types.
// defaultFilter: rule to process the default values returned by the defaultKey rule. It updates the default values object.
// freeTextFilter: rule to process the free text search values returned by the identifier rule. It builds filter Object.
// standardFilter: rule to process the values returned by the key rule. It builds filter Object.
// key: rule to match pre-defined search syntax fields, e.g. amount, merchant, etc
// defaultKey: rule to match pre-defined search syntax fields that are used to update default values, e.g. type, status, etc
// identifier: composite rule to match patterns defined by the quotedString and alphanumeric rules

// filter, logicalAnd, operator, alphanumeric, quotedStrig are defined in baseRules.peggy grammar

// global initializer (code executed only once)
{{// CAUTION: DO NOT DIRECTLY ALTER OR MODIFY `searchParser.js` OR `autocompleteParser.js`
// These files are auto-generated by Peggy from grammar files (*.peggy). 
// To make changes, edit the corresponding *.peggy files only.
// Use the `generate-search-parser` and `generate-autocomplete-parser` scripts to regenerate parsers after modifications.

  function buildFilter(operator, left, right) {
    return { operator, left, right };
  }
}}

// per-parser initializer (code executed before every parse)
{
  const defaultValues = {
    type: "expense",
    status: "",
    sortBy: "date",
    sortOrder: "desc",
  };

  function applyDefaults(filters) {
    return {
      ...defaultValues,
      filters,
    };
  }

  function updateDefaultValues(field, value) {
    defaultValues[field] = value;
  }
}

query = _ filters:filterList? _ { return applyDefaults(filters); }

filterList
  = head:filter tail:(logicalAnd filter)* {
      const allFilters = [head, ...tail.map(([_, filter]) => filter)]
        .filter(Boolean)
        .filter((filter) => filter.right);
      if (!allFilters.length) {
        return null;
      }

      const keywords = allFilters.filter(
        (filter) =>
          filter.left === "keyword" || filter.right?.left === "keyword"
      );
      const nonKeywords = allFilters.filter(
        (filter) =>
          filter.left !== "keyword" && filter.right?.left !== "keyword"
      );

      const keywordFilter = buildFilter(
        "eq",
        "keyword",
        keywords
          .map((filter) => filter.right.replace(/^(['"])(.*)\1$/, "$2"))
          .flat()
      );
      if (keywordFilter.right.length > 0) {
        nonKeywords.push(keywordFilter);
      }
      return nonKeywords.reduce((result, filter) =>
        buildFilter("and", result, filter)
      );
    }

filter = @(standardFilter / defaultFilter / freeTextFilter)

defaultFilter
  = _ key:defaultKey _ op:operator _ value:identifier {
      updateDefaultValues(key, value);
    }

freeTextFilter
  = _ value:(quotedString / [^ \t\r\n\xA0]+) _ {
      //handle no-breaking space
      let word;
      if (Array.isArray(value)) {
        word = value.join("");
      } else {
        word = value;
      }
      return buildFilter("eq", "keyword", word);
    }

standardFilter
  = _ field:filterKey _ op:filterOperator _  values:identifier {
      expectingNestedQuote = false; nameOperator = false;
      return buildFilter(op, field, values);
    }

key "key"
  = @(
      date
      / amount
      / merchant
      / description
      / reportID
      / keyword
      / in
      / currency
      / groupCurrency
      / tag
      / category
      / to
      / taxRate
      / cardID
      / from
      / payer
      / exporter
      / expenseType
      / withdrawalType
      / submitted
      / approved
      / paid
      / exported
      / posted
      / withdrawn
      / feed
      / title
      / assignee
      / createdBy
      / reimbursable
      / billable
      / action
    )

filterKey
  = k:key {
      nameOperator = (k === "from" || k === "to");
      return k;
}

defaultKey "default key" = @(type / status / sortBy / sortOrder / policyID / groupBy)

identifier
  = (","+)? parts:(values / quotedString / alphanumeric)|1.., ","+| empty:(","+)? {
      const value = parts.flat().filter(Boolean).map((word) => {
        if (word.startsWith('"') && word.endsWith('"') && word.length >= 2) {
          return word.slice(1, -1);
        }
        return word;
      });
      if (value.length > 1) {
        return value.filter((word) => word.length > 0);
      }
      return value[0];
    }
