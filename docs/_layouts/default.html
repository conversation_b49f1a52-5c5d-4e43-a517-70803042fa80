<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Security-Policy" content="script-src https://kit.fontawesome.com https://cdnjs.cloudflare.com https://vjs.zencdn.net https://unpkg.com https://www.google.com https://www.gstatic.com/ https://cse.google.com http://cse.google.com https://partner.googleadservices.com/ https://cdnjs.cloudflare.com/ajax/libs/tocbot/ https://www.googletagmanager.com 'self' 'unsafe-eval' 'unsafe-inline'; style-src cdnjs.cloudflare.com vjs.zencdn.net unpkg.com cse.google.com www.google.com ka-f.fontawesome.com fonts.googleapis.com 'self' 'unsafe-inline'; worker-src blob: 'self';" >
    <title>Expensify Help</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, minimal-ui">
    <link rel="icon" type="image/png" href="{{ '/assets/images/expensify-logo-round.png' | cache_bust }}">
    <link rel="stylesheet" href="{{ site.main_style | cache_bust}}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tocbot/4.12.0/tocbot.css">
    <link rel="stylesheet" href="https://vjs.zencdn.net/8.5.2/video-js.css" />
    <link rel="stylesheet" href="https://unpkg.com/@videojs/themes@1/dist/fantasy/index.css" />

    <script src="https://kit.fontawesome.com/263e5e8608.js" crossorigin="anonymous"></script>
    <script defer src="https://cdnjs.cloudflare.com/ajax/libs/tocbot/4.12.0/tocbot.js"></script>
    <script defer src="https://vjs.zencdn.net/8.5.2/video.min.js"></script>
    <script defer src="{{ '/assets/js/main.js' | cache_bust }}"></script>
    <script defer src="{{ '/assets/js/platform-tabs.js' | cache_bust }}"></script>
    <script defer src="{{ '/assets/js/selector.js' | cache_bust }}"></script>
    <script async src="https://cse.google.com/cse.js?cx=4149bc31c5c314a1a"></script>
    <!-- Google Tag Manager -->
    <script>(function (w, d, s, l, i) { w[l] = w[l] || []; w[l].push({ 'gtm.start': new Date().getTime(), event: 'gtm.js' }); var f = d.getElementsByTagName(s)[0], j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f); })(window, document, 'script', 'dataLayer', 'GTM-TQBQW7CR');</script>
    <!-- End Google Tag Manager -->

    <!-- SEO tags -->
    {% seo %}
</head>
<body>
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-TQBQW7CR" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <div id="lhn">

        <div class="lhn-header">
            <div class="flex">
                <div id="header-button">
                    <img id="angle-up-icon" src="/assets/images/arrow-up.svg" class="base-icon hide"></img>
                    <img id="bars-icon" src="/assets/images/menu.svg" class="base-icon"></img>
                </div>
                <div>
                    <a href="/">
                        <svg class="logo" data-source="local" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="Layer_1" x="0" y="0" version="1.1" viewBox="0 0 189 30" style="enable-background:new 0 0 189 30" xml:space="preserve">
                            <g><g><path d="M0,23.1h13.9V19H5.1v-3.8h7.1V11H5.1V7.4h8.8V3.3H0V23.1z" class="expensifyhelp-logo__expensify"/><path d="M31.1,7.7H25l-2,3.7l-2.1-3.7h-6.3l5.1,7.5l-5.5,8h6.1l2.4-4.1l2.4,4.1h6.3l-5.5-8L31.1,7.7z" class="expensifyhelp-logo__expensify"/><path d="M40.8,7.3L40.8,7.3c-1.7,0-3.2,0.7-4.2,2V7.7h-4.7v22h5v-7.7c0.9,0.9,2.3,1.5,3.9,1.5c4.1,0,6.9-3.1,6.9-8.1 C47.7,10.5,45,7.3,40.8,7.3L40.8,7.3z M39.7,19.4c-1.9,0-3-1.5-3-4s1.1-4,3-4c1.9,0,3,1.4,3,4S41.5,19.4,39.7,19.4z" class="expensifyhelp-logo__expensify"/><path d="M56.4,19.6c-1.5,0-2.7-0.9-3-3.1h10.6v-0.7c0-4.8-2.7-8.5-7.9-8.5c-3.7,0-7.9,2-7.9,8.1 c0,4.9,3.2,8.1,8.1,8.1c2.9,0,6.6-1.1,7.7-5.6h-5.1C58.9,18.7,58.2,19.6,56.4,19.6L56.4,19.6z M56.2,11c1.4,0,2.4,0.8,2.6,2.6 h-5.2C54,11.6,55,11,56.2,11L56.2,11z" class="expensifyhelp-logo__expensify"/><path d="M73.9,7.3c-1.7,0-3.2,0.8-4.2,2.2V7.7h-4.7v15.4h5v-8.6c0-2.1,1.1-3,2.4-3c1.5,0,2.3,0.6,2.3,3v8.6h5v-9 C79.7,9.2,77.7,7.3,73.9,7.3L73.9,7.3z" class="expensifyhelp-logo__expensify"/><path d="M97.9,0.7c-1.5,0-2.6,1.1-2.6,2.6c0,1.4,1.1,2.6,2.6,2.6c1.5,0,2.6-1.2,2.6-2.6C100.5,1.8,99.5,0.7,97.9,0.7z" class="expensifyhelp-logo__expensify"/><path d="M100.4,7.7h-5v15.4h5V7.7z" class="expensifyhelp-logo__expensify"/><path d="M88.9,13.5l-1.2-0.3c-1.3-0.3-1.8-0.9-1.8-1.4c0-0.8,0.7-1.4,1.9-1.4c1.5,0,2.2,0.5,2.2,1.9h4.3 c-0.1-3.2-2-5.1-6.8-5.1c-4.1,0-6.6,1.5-6.6,5c0,2.7,1.8,3.9,6,4.9l1.2,0.3c1.3,0.3,1.8,0.9,1.8,1.5c0,0.8-0.8,1.2-2.1,1.2 c-1.7,0-2.5-0.6-2.7-2h-4.9c0.3,3.7,2.9,5.6,7.4,5.6c4.5,0,7.2-1.7,7.2-5.1C94.9,15.7,93.1,14.4,88.9,13.5L88.9,13.5z" class="expensifyhelp-logo__expensify"/><path d="M119.9,7.7L119.9,7.7l-2.8,9.4l-2.7-9.4h-6.5V7.2c0-1.3,0.9-2.5,2.4-2.5h0.7V0.6h-1c-4.3,0.1-7.1,3.1-7.1,6.9 v0.2h-1.9v4.5h1.9v10.8h5V12.3h2.8l3.7,10.4l-0.6,1.6c-0.5,1.2-1,1.5-2,1.5c-0.5,0-1.1,0-1.8-0.2l-0.3,4.1 c0.7,0.2,1.7,0.3,2.9,0.3c3,0,4.5-1.3,6-5.2L125,7.7L119.9,7.7L119.9,7.7z" class="expensifyhelp-logo__expensify"/></g><g><path d="M144.5,23.1c2.7,0,4.3-0.3,4.3-1.1c0-0.7-0.7-0.9-2.1-0.9c-0.4-0.1-0.5-1.5-0.5-3.2V6.9 c0-1.7,0.1-3.1,0.5-3.2c1.5-0.1,2.1-0.3,2.1-0.9c0-0.9-1.6-1.2-4.3-1.2c-2.8,0-4.4,0.3-4.4,1.2c0,0.6,0.7,0.8,2.1,0.9 c0.4,0.1,0.5,1.6,0.5,3.2v3.9c-0.1,0.3-0.3,0.4-1.1,0.4h-8.5c-0.8,0-1-0.1-1.1-0.4V6.9c0-1.7,0.1-3.1,0.5-3.2 c1.5-0.1,2.1-0.3,2.1-0.9c0-0.9-1.6-1.2-4.4-1.2c-2.7,0-4.3,0.3-4.3,1.2c0,0.6,0.7,0.8,2.1,0.9c0.4,0.1,0.4,1.6,0.4,3.2v10.9 c0,1.7-0.1,3.1-0.4,3.2c-1.5,0.1-2.1,0.3-2.1,0.9c0,0.8,1.6,1.1,4.3,1.1c2.8,0,4.4-0.3,4.4-1.1c0-0.7-0.7-0.9-2.1-0.9 c-0.4-0.1-0.5-1.5-0.5-3.2v-4.3c0.1-0.3,0.3-0.4,1.1-0.4h8.5c0.8,0,0.9,0.1,1.1,0.4v4.2c0,1.7-0.1,3.1-0.5,3.2 c-1.5,0.1-2.1,0.3-2.1,0.9C140.1,22.8,141.7,23.1,144.5,23.1z" class="expensifyhelp-logo__help"/><path d="M156.3,23.4c2.7,0,4.6-1.2,5.7-2.5c0.7-0.7,1-1.5,1-2c0-0.5-0.2-0.8-0.6-0.8c-0.3,0-0.5,0.2-0.8,0.5 c-0.9,0.9-2.2,1.9-4.3,1.9c-3,0-4.8-2.1-5-5.3h8.8c1.2,0,1.8-0.9,1.8-2.2c0-2.6-2.3-5.2-6.1-5.2c-4.1,0-7.5,3.3-7.5,8.1 C149.3,20.3,152.1,23.4,156.3,23.4z M152.4,13.6c0.4-2.6,2-4.2,4.3-4.2c2.1,0,3.4,1.4,3.4,3c0,0.8-0.5,1.2-1.5,1.2L152.4,13.6z" class="expensifyhelp-logo__help"/><path d="M167.6,23.1c2.3,0,3.7-0.3,3.7-1.1c0-0.6-0.6-0.7-1.7-0.8c-0.3-0.1-0.4-1.2-0.4-3.1V7.5 c0-2.1,0.1-3.8,0.1-4.8c0-1.1-0.2-1.6-1-1.6c-0.8,0-3,0.5-4.1,1c-0.4,0.2-0.6,0.4-0.6,0.8c0,0.4,0.4,0.7,2,0.8 c0.4,0.2,0.4,1.9,0.4,3.3V18c0,2.1,0,3-0.4,3.1c-1.1,0.1-1.7,0.2-1.7,0.8C163.9,22.8,165.2,23.1,167.6,23.1z" class="expensifyhelp-logo__help"/><path d="M175.7,28.6c2.9,0,4.4-0.2,4.4-1.1c0-0.6-0.6-0.7-2.3-0.9c-0.4-0.2-0.5-1.2-0.5-3.1v-1.9c1,1,2.6,1.7,4.6,1.7 c4.2,0,7.1-3.4,7.1-8.1c0-4.6-2.6-7.5-6.1-7.5c-2.5,0-4.7,1.5-5.6,3.6c0-0.8,0-1.4,0-1.8c0-1.1-0.2-1.6-1-1.6c-0.8,0-3,0.5-4,1 c-0.4,0.2-0.6,0.5-0.6,0.8c0,0.4,0.4,0.7,1.9,0.8c0.4,0.2,0.4,2,0.4,3.2v9.7c0,2.1-0.1,3-0.4,3.1c-1.2,0.1-1.8,0.3-1.8,0.9 C171.9,28.3,173.3,28.6,175.7,28.6z M177.3,16.2c0-4.4,2.2-6.2,4.3-6.2c2.3,0,4,2.2,4,5.9c0,3.6-1.6,5.8-4.2,5.8 C179.1,21.7,177.3,20.2,177.3,16.2z" class="expensifyhelp-logo__help"/></g></g>
                        </svg>
                    </a>
                </div>
                <!-- Search icons that'll open the Sidebar Search sections -->
                {% include search-toggle.html %}
            </div>
        </div>

        <!-- Sidebar Search sections -->
        {% include sidebar-search.html id="sidebar-layer" %}

        <!-- LHN content (Main navigation tree or Article TOC) -->
        <div id="lhn-content">
            {% if page.url == "/" or page.url contains "/hubs/" %}
                {% include lhn-template.html %}
            {% else %}
                <div id="back-button" class="icon-with-link">
                    <img src="/assets/images/back-left.svg" class="base-icon"></img>
                    <div class="link">Back</div>
                </div>
                <div class="article-toc"></div>
            {% endif %}
        </div>
    </div>

    <div id="content-area">

        <div id="article-content">
            <!-- Article title (only shown in article pages) -->
            {% if page.url contains "/articles/" %}
            <div class="title-platform-tabs">
                <h1 class="title">
                    {{ page.name | remove: '.md' | split: "-" |  join: " " }}
                </h1>
                <div id="platform-tabs">
                </div>
            </div>
            <div class="article-toc-content article">
                {{ content }}
            </div>
            {% else %}
            <div class="article-toc-content lhn">
                {{ content }}
            </div>
            {% endif %}
        </div>

        {% if page.url contains "/articles/" %}
        <div>
            <a href="https://github.com/Expensify/App/blob/main/docs{{ page.url | remove: '.html' }}.md" target="_blank" id="edit-link">
                <svg id="edit-icon" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="Layer_1" x="0" y="0" version="1.1" viewBox="0 0 20 20" style="enable-background:new 0 0 20 20" xml:space="preserve"><path d="M17,8l-5-5l2.3-2.3c0.4-0.4,1-0.4,1.4,0l3.6,3.6c0.4,0.4,0.4,1,0,1.4L17,8z"/><path d="M7.3,16.3l-3.6-3.6c-0.4-0.4-0.4-1,0-1.4l6.6-6.6c0.4-0.4,1-0.4,1.4,0l3.6,3.6c0.4,0.4,0.4,1,0,1.4l-6.6,6.6	C8.3,16.7,7.7,16.7,7.3,16.3z"/><path d="M2,15l-1,4l4-1L2,15z"/></svg>
                Edit this page
            </a>
        </div>
        {% endif %}

        <!-- Concierge button at the bottom of the page -->
        {% include CONST.html %}
        <div class="get-help">
            <h2 class="title with-margin">Didn't find what you were looking for?</h2>
            <p class="description">Concierge is here to answer all your questions.</p>
            <a href="{{ CONCIERGE_CHAT_URL }}" target="_blank">
                <div class="submit-button">
                    <button class="success">Send a message</button>
                </div>
            </a>
        </div>

        <div id="footer">
            {% include footer.html %}
        </div>
    </div>
</body>
</html>
