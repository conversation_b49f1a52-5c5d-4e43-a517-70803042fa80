@import 'breakpoints';
@import 'colors';
@import 'fonts';
@import 'search-bar';

* {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    vertical-align: baseline;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

html {
    line-height: 1;

    @include maxBreakpoint($breakpoint-tablet) {
        scroll-padding-top: 80px;
    }
}

table {
    border-spacing: 0;
    border-collapse: collapse;
}

caption,
th,
td {
    text-align: left;
    font-weight: 400;
    vertical-align: middle;
}

q,
blockquote {
    quotes: none;

    &:before,
    &:after {
        content: none;
    }
}

article,
aside,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section {
    display: block;
}

html,
body {
    height: 100%;
    min-height: 100%;
    background: var(--color-appBG);
    color: var(--color-text-supporting);
}

hr {
    background: var(--color-borders);
    border: none;
    display: inline-block;
    width: 24px;
    height: 2px;
    margin: 0 0 16px;
}

strong {
    font-weight: bold;
}

em {
    font-style: italic;
}

a {
    color: var(--color-link);
    text-decoration: none;

    img {
        display: block;
    }
}

h1,
h2,
h3,
h4,
h5,
h6,
summary {
    color: var(--color-text);
    font-weight: bold;
    padding-bottom: 12px;
}

h2,
h3,
h4,
h5,
h6 {
    margin-top: 20px;
}

#faq::marker {
    font-size: 1.5em;
}

details summary {
    cursor: pointer;
    user-select: none;
}

details > summary {
    list-style-image: url('/assets/images/arrow-right.svg');
}

details[open] > summary {
    list-style-image: url('/assets/images/down.svg');
}

h1,
summary {
    font-family: 'Expensify New Kansas', 'Helvetica Neue', 'Helvetica', Arial, sans-serif;
    font-weight: 500;
    font-size: larger;
}

h2 {
    font-size: large;
}

p {
    padding-bottom: 20px;
}

code {
    font-family: 'Expensify Mono', 'Helvetica Neue', 'Helvetica', Arial, sans-serif;
    border-width: 1px;
    border-style: solid;
    border-radius: 5px;
    border-color: var(--color-borders);
    background-color: var(--color-highlightBG);
    line-height: 20px;
    padding-left: 5px;
    padding-right: 5px;
    font-size: 13px;
    margin-top: 0px;
    margin-bottom: 0px;
}

body,
button,
input,
select,
textarea {
    line-height: 1.33;
    font-weight: 400;
    font-family: 'Expensify Neue', 'Helvetica Neue', 'Helvetica', Arial, sans-serif;
    font-size: 16px;
    color: var(--color-text-supporting);
}

button {
    border-radius: 12px;
    padding: 12px;
    font-family: 'Expensify Neue', 'Helvetica Neue', 'Helvetica', Arial, sans-serif;
    font-size: 15px;
    font-weight: bold;

    &.success {
        background-color: var(--color-button-success-background);
        color: var(--color-button-text);
        width: 100%;
        border-radius: 100px;
        padding-left: 20px;
        padding-right: 20px;

        &:hover {
            background-color: var(--color-button-success-background-hover);
            cursor: pointer;
        }

        @include breakpoint($breakpoint-tablet) {
            width: auto;
        }
    }
}

#logo {
    width: 240px;
    padding: 80px;
    display: block;
    margin-left: auto;
    margin-right: auto;
    align-content: center;
}

.flex {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -moz-flex;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-flow: row wrap;
    flex-flow: row wrap;
    align-content: space-between;
}

#lhn {
    position: fixed;
    background-color: var(--color-highlightBG);
    box-sizing: border-box;
    border-right-color: var(--color-borders);
    border-right-width: 1px;
    border-style: solid;
    width: 100%;
    height: auto;
    &.expanded {
        height: 100%;
    }

    @include breakpoint($breakpoint-tablet) {
        width: 320px;
        height: 100%;
    }

    @include breakpoint($breakpoint-desktop) {
        width: 420px;
    }

    ul,
    li {
        list-style: none;
    }

    #lhn-content {
        overflow: auto;
        display: none;
        height: calc(100% - 100px);
        padding: 0 24px 24px 24px;

        .in-this-article {
            padding-top: 40px;
            display: block;
        }

        @include breakpoint($breakpoint-tablet) {
            display: block;
            height: calc(100% - 150px);
            padding: 0 44px 44px 44px;
            -ms-overflow-style: none;
            /* IE and Edge */
            scrollbar-width: none;
            /* Firefox */
            &::-webkit-scrollbar {
                display: none;
            }
        }

        &.expanded {
            display: block;
        }

        .article-toc {
            margin-top: 20px;
        }
    }

    .lhn-header {
        padding: 24px;

        @include breakpoint($breakpoint-tablet) {
            padding: 44px;
        }

        #header-button {
            display: block;
            padding-right: 24px;

            @include breakpoint($breakpoint-tablet) {
                display: none;
            }
        }

        .logo {
            width: 160px;
            align-content: center;
            display: block;
            margin-left: auto;
            margin-right: auto;

            @include breakpoint($breakpoint-desktop) {
                width: 180px;
                align-content: normal;
                display: flex;
                margin-left: 0;
                margin-right: 0;
            }
        }
    }

    .icon-with-link {
        display: grid;
        grid-template-columns: 40px auto;
        cursor: pointer;
        align-items: center;
    }

    .selected {
        font-weight: bold;
        color: var(--color-text);
    }

    .hide {
        display: none;
        position: fixed;
    }
}

#content-area {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    margin-left: 0;
    padding: 80px 24px 0px 24px;

    @include breakpoint($breakpoint-tablet) {
        margin-left: 320px;
    }

    @include breakpoint($breakpoint-desktop) {
        /* Same as the width of the lhn */
        margin-left: 420px;
        padding: 52px 68px 0 68px;
        box-sizing: border-box;
    }

    @include breakpoint($breakpoint-wide) {
        margin-left: 420px;
        /* On wide screens, the padding needs to be equal to
        the view width, minus the content size, minus the lhn size, divided by two. */
        padding: 52px calc((100vw - 1000px - 420px) / 2) 0 calc((100vw - 1000px - 420px) / 2);
    }

    ul,
    ol {
        margin-left: 24px;
        padding-bottom: 20px;
    }

    h1 {
        &.title {
            font-size: 2.25em;
            flex: 1;
        }
    }

    .article {
        .hidden {
            display: none;
        }
        img {
            display: block;
            margin: 20px auto;
            border-radius: 16px;

            @include maxBreakpoint($breakpoint-tablet) {
                width: 100%;
                height: 100%;
                max-width: auto;
                max-height: auto;
            }

            @include breakpoint($breakpoint-tablet) {
                max-width: -webkit-fill-available;
                max-height: -webkit-fill-available;
            }
        }

        li {
            padding-bottom: 4px;
        }

        ol {
            li {
                ul {
                    padding-bottom: 0;
                }
            }
        }

        table {
            margin-bottom: 20px;
            border-radius: 8px;

            // Box shadow is used here because border-radius and border-collapse don't work together. It leads to double borders.
            // https://stackoverflow.com/questions/628301/the-border-radius-property-and-border-collapsecollapse-dont-mix-how-can-i-use
            border-style: hidden;
            box-shadow: 0 0 0 1px var(--color-borders);
        }

        th:first-child {
            border-top-left-radius: 8px;
        }

        th:last-child {
            border-top-right-radius: 8px;
        }

        tr:last-child > td:first-child {
            border-bottom-left-radius: 8px;
        }

        tr:last-child > td:last-child {
            border-bottom-right-radius: 8px;
        }

        th,
        td {
            padding: 6px 13px;
            border: 1px solid var(--color-borders);
        }

        thead tr th {
            font-weight: bold;
            background-color: var(--color-highlightBG);
        }

        .img-wrap {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
        }

        h1,
        h2,
        h3 {
            line-height: 1.2;
        }

        h1,
        summary {
            font-size: 1.5em;
            padding: 20px 0 12px 0;
        }

        h2 {
            font-size: 1.125em;
            font-weight: 500;
            font-family: 'Expensify New Kansas', 'Helvetica Neue', 'Helvetica', Arial, sans-serif;
        }

        h3 {
            font-size: 1em;
            font-family: 'Expensify Neue', 'Helvetica Neue', 'Helvetica', Arial, sans-serif;
        }

        h2,
        h3 {
            margin: 0;
            padding: 12px 0 12px 0;
        }

        blockquote {
            margin-top: 20px;
            margin-bottom: 20px;
            padding-top: 20px;
            padding-left: 5%;
            border-left: 5px solid var(--color-button-background-hover);

            em:before {
                content: '\“\a';
                white-space: pre;
                font-size: 60px;
                line-height: 1em;
                color: var(--color-accent);
            }

            p:first-child {
                font-size: large;
                font-family: 'Expensify New Kansas', 'Helvetica Neue', 'Helvetica', Arial, sans-serif;
                opacity: 0.8;
            }
        }

        .selector-container {
            background-color: var(--color-highlightBG);
            display: flex;
            flex-direction: row-reverse;
            gap: 20px;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            justify-content: space-between;
            * > ol,
            ul {
                padding: 0;
            }

            @include maxBreakpoint($breakpoint-tablet) {
                flex-direction: column;
            }
        }

        select {
            height: 28px;
            border-radius: 20px;
            padding: 0px 26px 0px 12px;
            color: var(--color-text);
            font-size: 11px;
            font-weight: 700;
            text-align: center;
            cursor: pointer;

            @include maxBreakpoint($breakpoint-tablet) {
                width: 100px;
            }
        }

        select {
            background: url('/assets/images/down.svg') no-repeat right var(--color-button-background);
            background-size: 12px;
            background-position-x: 85%;
            appearance: none !important;
            -moz-appearance: none !important;
            -webkit-appearance: none !important;
        }

        .info {
            padding: 12px;
            margin-bottom: 20px;
            border-radius: 8px;
            background-color: var(--color-highlightBG);
            color: var(--color-text);
            display: flex;
            gap: 12px;
            align-items: center;

            img {
                height: 16px;
                width: 16px;
            }

            * {
                padding: 0;
                margin: 0;
            }

            li {
                margin-left: 12px;
            }
        }

        .video-container {
            position: relative;
            margin-bottom: 20px;
        }

        .video-js {
            width: 100%;
            border-radius: 8px;
            background-size: cover;
            background-repeat: no-repeat;
            background-size: cover;
            aspect-ratio: 16 / 9;
            background-repeat: no-repeat;

            .vjs-tech {
                border-radius: 8px;
            }

            .vjs-button {
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 1 !important;
                visibility: visible !important;
            }

            .vjs-big-play-button {
                background-image: url('/assets/images/play-button.svg');
                background-repeat: no-repeat;
                background-size: 72px;
                height: 72px;
                width: 72px;
                background-position: center;
                border: none !important;
                box-shadow: none !important;
                background-color: transparent !important;

                .vjs-icon-placeholder {
                    display: none;
                }
            }

            .vjs-play-progress {
                background-color: var(--color-white);
            }

            .vjs-control-bar {
                border-radius: 8px;
                background-color: rgba(6, 27, 9, 0.8);
                color: var(--color-white);
                width: calc(100% - 16px);
                bottom: 8px;
                left: 8px;

                .vjs-playing,
                .vjs-paused {
                    color: var(--color-white);
                    height: 100%;
                    width: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 4px;
                    margin-left: 12px;
                }

                .vjs-playing {
                    background: url('/assets/images/pause.svg');
                    background-repeat: no-repeat;
                    background-position: center;
                    background-size: contain;
                    border: none !important;
                    outline: none !important;
                    box-shadow: none !important;
                }

                .vjs-paused {
                    background: url('/assets/images/play.svg');
                    background-repeat: no-repeat;
                    background-position: center;
                    background-size: contain;
                    border: none !important;
                    outline: none !important;
                    box-shadow: none !important;
                }

                .vjs-icon-placeholder:before {
                    font-size: 20px;
                }

                .vjs-fullscreen-control {
                    .vjs-icon-placeholder:before {
                        content: '';
                        background: url('/assets/images/fullscreen.svg');
                        background-repeat: no-repeat;
                        background-position: center;
                        background-size: contain;
                        width: 20px;
                        margin-left: 4px;
                    }
                }

                .vjs-control:focus {
                    text-shadow: none;
                }

                .vjs-progress-holder {
                    border-radius: 8px;
                    overflow: hidden;

                    .vjs-time-tooltip {
                        display: none;
                    }

                    .vjs-mouse-display {
                        display: none;
                    }
                }

                .vjs-load-progress {
                    background: var(--color-transparent-white);
                    border-radius: 8px;

                    div {
                        background-color: var(--color-transparent-white);
                        border-radius: 0 8px 8px 0;
                    }
                }

                .vjs-progress-control {
                    .vjs-play-progress {
                        border-radius: 8px;
                    }

                    .vjs-play-progress::before {
                        display: none;
                    }
                }

                .vjs-play-control {
                    .vjs-icon-placeholder:before {
                        display: none;
                    }
                }
            }
        }
    }
}

.link {
    display: inline;
    color: var(--color-text-supporting);
    cursor: pointer;

    &:hover {
        color: var(--color-link);
    }
}

.lhn-items {
    ol,
    ul {
        padding-left: 32px;
    }

    ul,
    li {
        margin-bottom: 20px;

        /* Apply recursive style to add a margin to the nested items */
        ul,
        li {
            margin-top: 20px;
            cursor: pointer;
        }
    }

    .nested-treeview {
        margin-left: 40px;
    }

    .selected-article {
        font-weight: bold;
        color: var(--color-text);
    }

    .home-link {
        padding-left: 40px;
    }
}

.cards-group {
    display: grid;
    grid-template-columns: auto;
    row-gap: 20px;
    column-gap: 20px;
    padding-bottom: 20px;

    @include breakpoint($breakpoint-desktop) {
        grid-template-columns: 50% 50%;
    }
}

.platform-cards-group {
    @extend .cards-group;

    @include breakpoint($breakpoint-desktop) {
        grid-template-columns: 33.33% 33.33% 33.33%;
    }
}

.card {
    display: flex;
    flex-wrap: nowrap;
    border-radius: 16px;
    padding: 28px;
    font-weight: 700;
    cursor: pointer;
    color: var(--color-text);
    background-color: var(--color-highlightBG);

    &:hover {
        background-color: var(--color-row-hover);
    }

    .row {
        display: flex;
        flex-basis: 100%;
    }

    .body {
        display: flex;
        flex-wrap: nowrap;
        flex-direction: column;
        flex-grow: 2;
    }

    h3.title {
        font-family: 'Expensify New Kansas', 'Helvetica Neue', 'Helvetica', Arial, sans-serif;
    }

    h3.title,
    h4.title {
        padding: 0;
        margin: 0;
    }

    p.description,
    p.url {
        margin: 0;
        font-weight: normal;
    }
}

.article-card {
    @extend .card;

    .right-icon {
        display: flex;
        align-items: center;
        padding-left: 16px;
    }
}

.platform-card {
    @extend .card;
    .row {
        flex-direction: column;
    }

    .platform-screenshot {
        display: flex;
        align-items: center;

        img {
            border-radius: 16px;
            width: 100%;
        }
    }

    .submit-button {
        display: flex;
        align-items: center;
        margin-top: 16px;
        padding-left: 0;

        @include breakpoint($breakpoint-desktop) {
            margin-top: 0;
            padding-left: 16px;
        }
    }

    .body {
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .select-button {
                display: flex;
                .success {
                    align-items: flex-end;
                    font-size: 0.8em;
                }
            }
        }
    }

    h3.title,
    h4.title {
        &.with-margin {
            margin: 0 0 4px 0;
        }
    }

    h3.title {
        font-size: 1.4em;
        font-weight: normal;
    }

    p.description {
        color: var(--color-text-supporting);
        padding: 20px 0 20px 0;
    }

    p.url {
        padding: 0;
        font-size: 0.8em;
        color: var(--color-text-supporting);
    }
}

.hub-card {
    @extend .card;
    padding: 24px;

    .row {
        flex-direction: column;
    }

    h3.title {
        font-size: 1.2em;
        font-weight: normal;
        &.with-margin {
            margin: 20px 0 8px 0;
        }
    }

    p.description {
        padding: 0;
        color: var(--color-text-supporting);

        &.with-min-height {
            min-height: 68px;

            @include breakpoint($breakpoint-tablet) {
                min-height: 48px;
            }
        }
    }
}

.base-icon {
    width: 20px;
    height: 20px;
    cursor: pointer;
    display: inline-block;
}

.homepage {
    h1 {
        margin-top: 0;
        padding-bottom: 20px;
        padding-top: 8px;
        @include breakpoint($breakpoint-tablet) {
            padding-top: 0px;
        }
    }
    h2 {
        padding-bottom: 24px;
    }
    p {
        margin-bottom: 20px;
    }
    .cards-group {
        padding-bottom: 32px;
    }
}

.centered-content {
    width: 100%;
    height: calc(100vh - 56px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;

    div {
        margin-top: 8px;
    }

    strong {
        font-size: 20px;
    }

    .icon {
        width: 76px;
        padding: 28px;
        display: block;
        margin-left: auto;
        margin-right: auto;
    }
}

.get-help {
    flex-wrap: wrap;
    margin-top: 40px;
}

.disable-scrollbar {
    @media screen and (max-width: $breakpoint-tablet) {
        overflow: hidden;
    }
}

.page-footer {
    font-size: 15px;

    @include maxBreakpoint($breakpoint-desktop) {
        background: url('/assets/images/expensify-footer-logo--vertical.svg') no-repeat right 120px;
        background-size: 111px 618px;
        margin-right: -25px;
    }

    h3 {
        color: var(--color-success);
        font-family: 'Expensify New Kansas', 'Helvetica Neue', 'Helvetica', Arial, sans-serif;
        font-size: 17px;
        font-weight: 500;
        padding: 0;
        margin-bottom: 16px;
        margin-top: 0;
    }

    ul {
        margin: 0px !important;
        padding: 0;

        li {
            list-style-type: none !important;
            margin: 0 0 8px;

            a {
                color: var(--color-text-supporting);
                display: block;
                padding: 4px 0;
                word-break: break-word;

                &:hover {
                    color: var(--color-link);
                }
            }
        }
    }

    &__social-icons {
        padding-bottom: 20px;

        a {
            color: var(--color-text-supporting);
            display: inline-block;

            &:hover {
                color: var(--color-link);
            }
        }
    }

    &__fine-print {
        color: var(--color-text-supporting);
        font-size: 10px;

        a {
            color: var(--color-link);
        }
    }

    // Big logo at the bottom
    &__logo {
        margin-top: 40px;

        img {
            display: block;
        }

        @include maxBreakpoint($breakpoint-desktop) {
            display: none;
        }
    }

    &__wrapper {
        margin: 0 auto;
        max-width: 1000px;
        padding: 64px 0 0;

        @include maxBreakpoint($breakpoint-tablet) {
            padding: 64px 0px 20px;
        }
    }

    .columns {
        @include breakpoint($breakpoint-desktop) {
            display: flex;
            margin-left: (-1 * 16px);
            margin-right: (-1 * 16px);
        }
    }

    .column {
        margin-bottom: 40px;

        @include breakpoint($breakpoint-desktop) {
            padding: 16px;
            width: 25%;
        }
    }
}

.title-platform-tabs {
    display: flex;
    justify-content: space-between;
    padding-bottom: 20px;
    h1 {
        padding: 0;
    }

    @include maxBreakpoint($breakpoint-tablet) {
        flex-direction: column;
        gap: 8px;
    }
}

#platform-tabs {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    text-align: center;
    font-weight: 700;
    font-size: 13px;
    gap: 4px;
}

.badge{
    border-style: solid;
    border-width: 1px;
    border-color: var(--color-borders);
    background-color: transparent;
    border-radius: 4px;
    text-align: center;
    padding: 6px 12px;
    color: var(--color-text);
    font-size: 11px;
    box-sizing: border-box;
    height: 28px;
}

#platform-tabs > .active {
    color: var(--color-text);
    background-color: var(--color-button-background);
}

.hidden {
    display: none;
}

.expensifyhelp-logo__expensify {
    fill: var(--color-text);
}

.expensifyhelp-logo__help {
    fill: var(--color-success);
}

#edit-link {
    margin: 20px 0;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
}

#edit-icon {
    fill: var(--color-text-supporting);
    height: 13px;
    width: 13px;
}
