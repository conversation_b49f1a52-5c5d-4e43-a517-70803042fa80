# Per Diem Validation System Guide

## Overview

This guide explains the comprehensive per diem validation system that has been implemented, including how it differs from distance rate validation and how to use it in your components.

## Key Differences: Per Diem vs Distance Rate

### Structure Complexity

**Distance Rate:**
```typescript
// Single level structure
transaction.comment.customUnit = {
    customUnitID: "distance-unit-id",
    customUnitRateID: "rate-id",
    name: "Distance"
}
```

**Per Diem:**
```typescript
// Two-level hierarchical structure
transaction.comment.customUnit = {
    customUnitID: "perdiem-unit-id", 
    customUnitRateID: "destination-id",
    name: "Per Diem International",
    subRates: [
        {
            id: "subrate-1",
            name: "Breakfast",
            rate: 1500, // in cents
            quantity: 1
        },
        {
            id: "subrate-2", 
            name: "Lunch",
            rate: 2500,
            quantity: 1
        }
    ],
    attributes: {
        dates: {
            start: "2024-01-01",
            end: "2024-01-03"
        }
    }
}
```

### Validation Requirements

| Aspect | Distance Rate | Per Diem |
|--------|---------------|----------|
| **Custom Unit Name** | `NAME_DISTANCE` | `NAME_PER_DIEM_INTERNATIONAL` |
| **Required Fields** | customUnitID, customUnitRateID, waypoints | customUnitID, customUnitRateID, subRates, dates |
| **Filtering Logic** | Match customUnitID + customUnitRateID | Match customUnitID + customUnitRateID + validate subRates |
| **Deletion Check** | Check if rate has transactions | Check if rate OR specific subrate has transactions |

## Available Validation Functions

### 1. Core Validation Functions

```typescript
// Validate individual transaction eligibility
validatePerDiemTransactionEligibility(
    transaction: Transaction,
    customUnitID: string,
    rateID: string,
    subRateID?: string
): boolean

// Validate form inputs
validatePerDiemForm(
    values: PerDiemForm,
    customUnit: CustomUnit | undefined,
    toLocaleDigit: (arg: string) => string,
    selectedRate?: Rate,
    currentSubrateId?: string
): PerDiemFormErrors
```

### 2. Transaction Selector Functions

```typescript
// For specific rate/subrate details pages
createPerDiemEligibleTransactionsSelector(
    customUnitID: string,
    rateID: string,
    subRateID?: string
)

// For overview pages showing all per diem transactions
createPerDiemAllEligibleTransactionsSelector(
    customUnitID: string,
    rateIDs: Set<string>
)
```

### 3. Deletion Safety Functions

```typescript
// Check if a rate can be safely deleted
hasPerDiemRateTransactions(
    transactions: Record<string, Transaction> | null,
    customUnitID: string,
    rateID: string
): boolean

// Check if a subrate can be safely deleted
hasPerDiemSubRateTransactions(
    transactions: Record<string, Transaction> | null,
    customUnitID: string,
    rateID: string,
    subRateID: string
): boolean
```

## Implementation Examples

### 1. Per Diem Details Page

```typescript
function PolicyPerDiemRateDetailsPage({route}) {
    const {policyID, rateID, subRateID} = route.params;
    const customUnit = getPerDiemCustomUnit(policy);
    
    // Create selector for this specific rate/subrate
    const eligibleTransactionsSelector = useMemo(() => {
        if (!customUnit?.customUnitID) return () => new Set<string>();
        return createPerDiemEligibleTransactionsSelector(
            customUnit.customUnitID,
            rateID,
            subRateID
        );
    }, [customUnit?.customUnitID, rateID, subRateID]);

    const [eligibleTransactionIDs] = useOnyx(ONYXKEYS.COLLECTION.TRANSACTION, {
        selector: eligibleTransactionsSelector,
        canBeMissing: true,
    });

    // Check if rate/subrate can be deleted
    const hasTransactions = eligibleTransactionIDs && eligibleTransactionIDs.size > 0;
}
```

### 2. Per Diem Overview Page

```typescript
function WorkspacePerDiemPage({route}) {
    const customUnit = getPerDiemCustomUnit(policy);
    const rateIDs = new Set(Object.keys(customUnit?.rates ?? {}));
    
    // Get all per diem transactions
    const [eligibleTransactionsData] = useOnyx(ONYXKEYS.COLLECTION.TRANSACTION, {
        selector: createPerDiemAllEligibleTransactionsSelector(
            customUnit?.customUnitID,
            rateIDs
        ),
        canBeMissing: true,
    });

    // Access transaction mappings
    const {
        transactionIDs,
        rateIDToTransactionIDsMap,
        subRateIDToTransactionIDsMap
    } = eligibleTransactionsData || {};
}
```

### 3. Form Validation

```typescript
function EditPerDiemAmountPage() {
    const validate = useCallback((values: PerDiemForm) => {
        return validatePerDiemForm(
            values,
            customUnit,
            toLocaleDigit,
            selectedRate,
            currentSubrateId
        );
    }, [customUnit, selectedRate, currentSubrateId, toLocaleDigit]);
}
```

## Transaction Violation Handling

```typescript
const [transactionViolations] = useOnyx(ONYXKEYS.COLLECTION.TRANSACTION_VIOLATIONS, {
    selector: (violations) => {
        if (!eligibleTransactionIDs || eligibleTransactionIDs.size === 0) {
            return undefined;
        }
        return Object.fromEntries(
            Object.entries(violations ?? {}).filter(([key]) => {
                const id = key.replace(ONYXKEYS.COLLECTION.TRANSACTION_VIOLATIONS, '');
                return eligibleTransactionIDs?.has(id);
            }),
        );
    },
    canBeMissing: true,
});
```

## Best Practices

### 1. Always Use Selectors
- Use `createPerDiemEligibleTransactionsSelector` for specific rate/subrate filtering
- Use `createPerDiemAllEligibleTransactionsSelector` for overview pages
- Memoize selectors to prevent unnecessary re-renders

### 2. Check Transaction Dependencies
- Always check `hasPerDiemRateTransactions` before deleting rates
- Always check `hasPerDiemSubRateTransactions` before deleting subrates
- Show appropriate warnings when transactions exist

### 3. Validate Form Inputs
- Use `validatePerDiemForm` for comprehensive form validation
- Use individual validation functions for specific field validation
- Handle both destination and subrate uniqueness validation

### 4. Handle Hierarchical Structure
- Remember that per diem has destinations (rates) containing subrates
- Filter transactions at both rate and subrate levels as needed
- Use the mapping objects for efficient transaction counting

## Error Handling

The validation system provides specific error messages for:
- Missing required fields
- Duplicate destinations/subrates
- Invalid amounts/dates
- Transaction dependency conflicts
- Custom unit mismatches

All error messages are localized and follow the existing pattern used in distance rate validation.
